/**
 * 工单列表主控制器
 */

class TicketListController {
    constructor() {
        this.currentUser = null;
        this.filters = {};
        this.pagination = {
            page: 1,
            pageSize: CONFIG.pagination.defaultPageSize,
            total: 0
        };
        this.sortField = CONFIG.table.defaultSortField;
        this.sortOrder = CONFIG.table.defaultSortOrder;
        
        this.table = null;
        this.autoRefreshTimer = null;
        
        this.init();
    }
    
    async init() {
        try {
            // 加载当前用户信息
            await this.loadCurrentUser();
            
            // 初始化组件
            this.initComponents();
            
            // 绑定事件
            this.bindEvents();
            
            // 加载数据
            await this.loadData();
            
            // 启动自动刷新
            this.startAutoRefresh();

            // 初始化角色定制化功能
            this.initializeRoleCustomization();

            // 初始化智能化功能
            this.initializeIntelligentFeatures();

            // 初始化个性化设置
            this.initializePersonalization();

            // 初始化智能排序
            this.initIntelligentSorting();

            // 增强批量操作
            this.enhanceBatchOperations();

            // 初始化实时协作
            this.initRealTimeCollaboration();

            console.log('工单列表初始化完成');
        } catch (error) {
            console.error('工单列表初始化失败:', error);
            Message.error('系统初始化失败，请刷新页面重试');
        }
    }
    
    /**
     * 加载当前用户信息
     */
    async loadCurrentUser() {
        try {
            const response = await API.getCurrentUser();
            if (response.success) {
                this.currentUser = response.data;
                this.updateUserInfo();
                this.updateRoleSpecificUI();
            }
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }
    
    /**
     * 更新用户信息显示
     */
    updateUserInfo() {
        const userRoleEl = DOM.$('#userRole');
        if (userRoleEl && this.currentUser) {
            const roleTexts = {
                municipal_operator: '市级话务员',
                district_operator: '区级话务员',
                street_operator: '街镇话务员',
                department_staff: '部门工作人员',
                executor: '执行人员',
                collaborator: '协办人',
                callback_staff: '回访员',
                manager: '管理者',
                leader: '领导',
                citizen: '市民'
            };
            userRoleEl.textContent = roleTexts[this.currentUser.role] || this.currentUser.role;
        }
    }
    
    /**
     * 更新角色特定UI
     */
    updateRoleSpecificUI() {
        if (!this.currentUser) return;

        // 角色专用筛选已隐藏，相关功能移至高级搜索
        /*
        const roleSpecificFilters = DOM.$('#roleSpecificFilters');
        if (roleSpecificFilters) {
            // 根据角色显示特定筛选器
            const roleClass = `role-${this.currentUser.role.replace('_', '-')}`;
            roleSpecificFilters.className = `role-specific-filters ${roleClass}`;

            // 显示角色特定的筛选选项
            if (this.currentUser.role === 'municipal_operator') {
                DOM.show(roleSpecificFilters);
            } else {
                DOM.hide(roleSpecificFilters);
            }
        }
        */

        // 根据权限显示/隐藏按钮
        this.updatePermissionBasedUI();
    }
    
    /**
     * 更新基于权限的UI
     */
    updatePermissionBasedUI() {
        const permissions = this.currentUser.permissions || [];
        
        // 合并按钮
        const mergeBtn = DOM.$('#batchMergeBtn');
        if (mergeBtn) {
            if (permissions.includes('merge')) {
                DOM.show(mergeBtn, 'inline-flex');
            } else {
                DOM.hide(mergeBtn);
            }
        }
        
        // 新建工单按钮
        const newBtn = DOM.$('#newTicketBtn');
        if (newBtn) {
            if (permissions.includes('create')) {
                DOM.show(newBtn, 'inline-flex');
            } else {
                DOM.hide(newBtn);
            }
        }
    }
    
    /**
     * 初始化组件
     */
    initComponents() {
        // 初始化表格
        const tableContainer = DOM.$('.table-section');
        if (tableContainer) {
            this.table = new TicketTable(tableContainer, {
                columns: this.getUserColumns()
            });
        }
        
        // 初始化筛选器
        this.initFilters();
        
        // 初始化分页
        this.initPagination();
    }
    
    /**
     * 获取用户角色对应的列配置
     */
    getUserColumns() {
        if (this.currentUser && CONFIG.columns[this.currentUser.role]) {
            return CONFIG.columns[this.currentUser.role];
        }
        return CONFIG.columns.default;
    }
    
    /**
     * 初始化筛选器
     */
    initFilters() {
        // 加载保存的筛选条件
        const savedFilters = UTILS.storage.get(CONFIG.storageKeys.userSettings);
        if (savedFilters && savedFilters.filters) {
            this.filters = savedFilters.filters;
            this.applyFiltersToUI();
        }
    }
    
    /**
     * 应用筛选条件到UI
     */
    applyFiltersToUI() {
        // 状态筛选
        if (this.filters.status) {
            const statusBtn = DOM.$(`#statusFilters [data-value="${this.filters.status}"]`);
            if (statusBtn) {
                DOM.$$('#statusFilters .filter-btn').forEach(btn => DOM.removeClass(btn, 'active'));
                DOM.addClass(statusBtn, 'active');
            }
        }
        
        // 紧急程度筛选
        if (this.filters.urgency) {
            const urgencyBtn = DOM.$(`#urgencyFilters [data-value="${this.filters.urgency}"]`);
            if (urgencyBtn) {
                DOM.$$('#urgencyFilters .filter-btn').forEach(btn => DOM.removeClass(btn, 'active'));
                DOM.addClass(urgencyBtn, 'active');
            }
        }
        
        // 处理模式筛选
        if (this.filters.mode) {
            const modeBtn = DOM.$(`#modeFilters [data-value="${this.filters.mode}"]`);
            if (modeBtn) {
                DOM.$$('#modeFilters .filter-btn').forEach(btn => DOM.removeClass(btn, 'active'));
                DOM.addClass(modeBtn, 'active');
            }
        }
        
        // 搜索框
        if (this.filters.search) {
            const searchInput = DOM.$('#searchInput');
            if (searchInput) {
                searchInput.value = this.filters.search;
            }
        }
    }
    
    /**
     * 初始化分页
     */
    initPagination() {
        const pageSizeSelect = DOM.$('#pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.value = this.pagination.pageSize;
        }
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选按钮事件
        this.bindFilterEvents();
        
        // 搜索事件
        this.bindSearchEvents();
        
        // 工具栏事件
        this.bindToolbarEvents();
        
        // 表格事件
        this.bindTableEvents();
        
        // 分页事件
        this.bindPaginationEvents();
        
        // 键盘快捷键
        this.bindKeyboardEvents();
    }
    
    /**
     * 绑定筛选事件
     */
    bindFilterEvents() {
        // 状态筛选
        EventHandler.delegate(document, '#statusFilters .filter-btn', 'click', (e) => {
            this.handleFilterClick(e.currentTarget, 'status');
        });
        
        // 紧急程度筛选
        EventHandler.delegate(document, '#urgencyFilters .filter-btn', 'click', (e) => {
            this.handleFilterClick(e.currentTarget, 'urgency');
        });
        
        // 处理模式筛选
        EventHandler.delegate(document, '#modeFilters .filter-btn', 'click', (e) => {
            this.handleFilterClick(e.currentTarget, 'mode');
        });
        
        // 角色特定筛选 - 已移除，功能整合到高级搜索
        /*
        const strategyFilter = DOM.$('#strategyFilter');
        if (strategyFilter) {
            EventHandler.on(strategyFilter, 'change', (e) => {
                this.filters.strategy = e.target.value;
                this.loadData();
            });
        }

        const mergeFilter = DOM.$('#mergeFilter');
        if (mergeFilter) {
            EventHandler.on(mergeFilter, 'change', (e) => {
                this.filters.merge = e.target.value;
                this.loadData();
            });
        }
        */
    }
    
    /**
     * 处理筛选按钮点击
     */
    handleFilterClick(button, filterType) {
        const value = button.dataset.value;
        const container = button.parentElement;
        
        // 更新UI状态
        DOM.$$('.filter-btn', container).forEach(btn => DOM.removeClass(btn, 'active'));
        DOM.addClass(button, 'active');
        
        // 更新筛选条件
        if (value === '') {
            delete this.filters[filterType];
        } else {
            this.filters[filterType] = value;
        }
        
        // 重新加载数据
        this.pagination.page = 1;
        this.loadData();
        this.saveUserSettings();
    }
    
    /**
     * 绑定搜索事件
     */
    bindSearchEvents() {
        const searchInput = DOM.$('#searchInput');
        const searchBtn = DOM.$('#searchBtn');
        
        if (searchInput) {
            // 防抖搜索
            const debouncedSearch = UTILS.debounce(() => {
                this.filters.search = searchInput.value.trim();
                this.pagination.page = 1;
                this.loadData();
                this.saveUserSettings();
            }, CONFIG.filter.debounceDelay);
            
            EventHandler.on(searchInput, 'input', debouncedSearch);
            EventHandler.on(searchInput, 'keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    debouncedSearch();
                }
            });
        }
        
        if (searchBtn) {
            EventHandler.on(searchBtn, 'click', () => {
                this.filters.search = searchInput.value.trim();
                this.pagination.page = 1;
                this.loadData();
                this.saveUserSettings();
            });
        }
        
        // 高级搜索
        const advancedSearchBtn = DOM.$('#advancedSearchBtn');
        if (advancedSearchBtn) {
            EventHandler.on(advancedSearchBtn, 'click', () => {
                this.showAdvancedSearch();
            });
        }
    }
    
    /**
     * 绑定工具栏事件
     */
    bindToolbarEvents() {
        // 新建工单
        const newTicketBtn = DOM.$('#newTicketBtn');
        if (newTicketBtn) {
            EventHandler.on(newTicketBtn, 'click', () => {
                this.createNewTicket();
            });
        }
        
        // 刷新
        const refreshBtn = DOM.$('#refreshBtn');
        if (refreshBtn) {
            EventHandler.on(refreshBtn, 'click', () => {
                this.refresh();
            });
        }
        
        // 批量合并
        const batchMergeBtn = DOM.$('#batchMergeBtn');
        if (batchMergeBtn) {
            EventHandler.on(batchMergeBtn, 'click', () => {
                this.batchMergeTickets();
            });
        }
        
        // 批量派单
        const batchDispatchBtn = DOM.$('#batchDispatchBtn');
        if (batchDispatchBtn) {
            EventHandler.on(batchDispatchBtn, 'click', () => {
                this.batchDispatchTickets();
            });
        }
        
        // 列设置
        const columnSettingsBtn = DOM.$('#columnSettingsBtn');
        if (columnSettingsBtn) {
            EventHandler.on(columnSettingsBtn, 'click', () => {
                this.showColumnSettings();
            });
        }
        
        // 导出
        const exportBtn = DOM.$('#exportBtn');
        if (exportBtn) {
            EventHandler.on(exportBtn, 'click', () => {
                this.exportData();
            });
        }
    }
    
    /**
     * 绑定表格事件
     */
    bindTableEvents() {
        if (!this.table) return;
        
        // 选择变化
        EventHandler.on(this.table.container, 'selection-change', (e) => {
            this.handleSelectionChange(e.detail);
        });
        
        // 排序变化
        EventHandler.on(this.table.container, 'sort-change', (e) => {
            this.sortField = e.detail.field;
            this.sortOrder = e.detail.order;
            this.loadData();
            this.saveUserSettings();
        });
        
        // 操作事件
        EventHandler.on(this.table.container, 'action', (e) => {
            this.handleTableAction(e.detail);
        });
    }
    
    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        // 页面大小变化
        const pageSizeSelect = DOM.$('#pageSizeSelect');
        if (pageSizeSelect) {
            EventHandler.on(pageSizeSelect, 'change', (e) => {
                this.pagination.pageSize = parseInt(e.target.value);
                this.pagination.page = 1;
                this.loadData();
                this.saveUserSettings();
            });
        }
    }
    
    /**
     * 绑定键盘事件
     */
    bindKeyboardEvents() {
        EventHandler.on(document, 'keydown', (e) => {
            // Ctrl+R 刷新
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.refresh();
            }
            
            // F5 刷新
            if (e.key === 'F5') {
                e.preventDefault();
                this.refresh();
            }
            
            // Ctrl+A 全选
            if (e.ctrlKey && e.key === 'a' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                if (this.table) {
                    const selectAllCheckbox = DOM.$('#selectAll');
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = true;
                        this.table.toggleSelectAll(true);
                    }
                }
            }
        });
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 显示加载状态
            this.showLoading();
            
            // 构建查询参数
            const params = {
                ...this.filters,
                page: this.pagination.page,
                pageSize: this.pagination.pageSize,
                sortField: this.sortField,
                sortOrder: this.sortOrder
            };
            
            // 请求数据
            const response = await API.getTicketList(params);
            
            if (response.success) {
                // 更新分页信息
                this.pagination = response.pagination;
                
                // 渲染表格
                if (this.table) {
                    this.table.render(response.data);
                }
                
                // 更新分页UI
                this.updatePaginationUI();
                
                // 加载统计数据
                await this.loadStats();
            } else {
                throw new Error(response.message || '加载数据失败');
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            Message.error('加载数据失败，请稍后重试');
            
            // 显示空状态
            if (this.table) {
                this.table.render([]);
            }
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 加载统计数据
     */
    async loadStats() {
        try {
            const response = await API.getStats();
            if (response.success) {
                this.updateStatsUI(response.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    /**
     * 更新统计UI
     */
    updateStatsUI(stats) {
        const elements = {
            urgentCount: DOM.$('#urgentCount'),
            timeoutCount: DOM.$('#timeoutCount'),
            pendingCount: DOM.$('#pendingCount'),
            mergeCount: DOM.$('#mergeCount')
        };
        
        if (elements.urgentCount) elements.urgentCount.textContent = stats.urgent || 0;
        if (elements.timeoutCount) elements.timeoutCount.textContent = stats.timeout || 0;
        if (elements.pendingCount) elements.pendingCount.textContent = stats.pending || 0;
        if (elements.mergeCount) elements.mergeCount.textContent = stats.merge || 0;
    }
    
    /**
     * 更新分页UI
     */
    updatePaginationUI() {
        const totalCountEl = DOM.$('#totalCount');
        if (totalCountEl) {
            totalCountEl.textContent = this.pagination.total;
        }
        
        // 生成分页按钮
        this.renderPaginationControls();
    }
    
    /**
     * 渲染分页控件
     */
    renderPaginationControls() {
        const container = DOM.$('#paginationControls');
        if (!container) return;
        
        const { page, totalPages } = this.pagination;
        const buttons = [];
        
        // 上一页
        buttons.push(`
            <button class="btn btn-outline btn-small" ${page <= 1 ? 'disabled' : ''} data-page="${page - 1}">
                ${CONFIG.icons.navigation?.prev || '◀'}
            </button>
        `);
        
        // 页码按钮
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);
        
        if (startPage > 1) {
            buttons.push(`<button class="btn btn-outline btn-small" data-page="1">1</button>`);
            if (startPage > 2) {
                buttons.push(`<span class="pagination-ellipsis">...</span>`);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            buttons.push(`
                <button class="btn ${i === page ? 'btn-primary' : 'btn-outline'} btn-small" data-page="${i}">
                    ${i}
                </button>
            `);
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                buttons.push(`<span class="pagination-ellipsis">...</span>`);
            }
            buttons.push(`<button class="btn btn-outline btn-small" data-page="${totalPages}">${totalPages}</button>`);
        }
        
        // 下一页
        buttons.push(`
            <button class="btn btn-outline btn-small" ${page >= totalPages ? 'disabled' : ''} data-page="${page + 1}">
                ${CONFIG.icons.navigation?.next || '▶'}
            </button>
        `);
        
        container.innerHTML = buttons.join('');
        
        // 绑定分页点击事件
        EventHandler.delegate(container, '[data-page]', 'click', (e) => {
            const newPage = parseInt(e.currentTarget.dataset.page);
            if (newPage !== this.pagination.page && newPage >= 1 && newPage <= totalPages) {
                this.pagination.page = newPage;
                this.loadData();
            }
        });
    }
    
    /**
     * 处理选择变化
     */
    handleSelectionChange(detail) {
        const { selectedCount } = detail;
        
        // 更新批量操作UI
        const batchInfo = DOM.$('#batchInfo');
        const selectedCountEl = DOM.$('#selectedCount');
        const batchMergeBtn = DOM.$('#batchMergeBtn');
        const batchDispatchBtn = DOM.$('#batchDispatchBtn');
        
        if (selectedCount > 0) {
            DOM.show(batchInfo);
            if (selectedCountEl) selectedCountEl.textContent = selectedCount;
            
            // 启用批量操作按钮
            if (batchMergeBtn) batchMergeBtn.disabled = false;
            if (batchDispatchBtn) batchDispatchBtn.disabled = false;
        } else {
            DOM.hide(batchInfo);
            
            // 禁用批量操作按钮
            if (batchMergeBtn) batchMergeBtn.disabled = true;
            if (batchDispatchBtn) batchDispatchBtn.disabled = true;
        }
    }
    
    /**
     * 处理表格操作
     */
    handleTableAction(detail) {
        const { action, ticketId } = detail;
        
        switch (action) {
            case 'view':
                this.viewTicketDetail(ticketId);
                break;
            case 'edit':
                this.editTicket(ticketId);
                break;
            case 'dispatch':
                this.dispatchTicket(ticketId);
                break;
            case 'follow':
                this.followTicket(ticketId);
                break;
            case 'close':
                this.closeTicket(ticketId);
                break;
            default:
                console.warn('未知操作:', action);
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoading() {
        const loadingState = DOM.$('#loadingState');
        const emptyState = DOM.$('#emptyState');
        
        if (loadingState) DOM.show(loadingState);
        if (emptyState) DOM.hide(emptyState);
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingState = DOM.$('#loadingState');
        if (loadingState) DOM.hide(loadingState);
    }
    
    /**
     * 刷新数据
     */
    async refresh() {
        await this.loadData();
        Message.success('数据已刷新');
    }
    
    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
        }
        
        this.autoRefreshTimer = setInterval(() => {
            this.loadData();
        }, CONFIG.table.autoRefreshInterval);
    }
    
    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
            this.autoRefreshTimer = null;
        }
    }
    
    /**
     * 保存用户设置
     */
    saveUserSettings() {
        const settings = {
            filters: this.filters,
            pagination: {
                pageSize: this.pagination.pageSize
            },
            sort: {
                field: this.sortField,
                order: this.sortOrder
            }
        };
        
        UTILS.storage.set(CONFIG.storageKeys.userSettings, settings);
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        this.stopAutoRefresh();
        
        // 清理事件监听器
        // 这里应该移除所有事件监听器，但由于使用了事件委托，大部分会自动清理
        
        console.log('工单列表组件已销毁');
    }
    
    // 占位方法，实际实现需要根据具体需求
    viewTicketDetail(ticketId) { console.log('查看工单详情:', ticketId); }
    editTicket(ticketId) { console.log('编辑工单:', ticketId); }
    dispatchTicket(ticketId) { console.log('派发工单:', ticketId); }
    followTicket(ticketId) { console.log('跟进工单:', ticketId); }
    closeTicket(ticketId) { console.log('关闭工单:', ticketId); }
    createNewTicket() { console.log('创建新工单'); }
    batchMergeTickets() { console.log('批量合并工单'); }
    batchDispatchTickets() { console.log('批量派发工单'); }
    showAdvancedSearch() {
        // 创建高级搜索弹窗
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content advanced-search-modal">
                <div class="modal-header">
                    <h3>高级搜索</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>处理策略：</label>
                                <select id="advancedStrategyFilter">
                                    <option value="">全部</option>
                                    <option value="instant">可即时办结</option>
                                    <option value="dispatch">需要派单</option>
                                    <option value="complex">复杂协调</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>合并机会：</label>
                                <select id="advancedMergeFilter">
                                    <option value="">全部</option>
                                    <option value="similar">有相似工单</option>
                                    <option value="mergeable">可合并工单</option>
                                    <option value="independent">独立工单</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>创建时间：</label>
                                <input type="date" id="advancedStartDate">
                                <span>至</span>
                                <input type="date" id="advancedEndDate">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="ticketListController.applyAdvancedSearch()">搜索</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    applyAdvancedSearch() {
        // 应用高级搜索条件
        const strategy = DOM.$('#advancedStrategyFilter')?.value || '';
        const merge = DOM.$('#advancedMergeFilter')?.value || '';
        const startDate = DOM.$('#advancedStartDate')?.value || '';
        const endDate = DOM.$('#advancedEndDate')?.value || '';

        // 更新筛选条件
        this.filters.strategy = strategy;
        this.filters.merge = merge;
        this.filters.startDate = startDate;
        this.filters.endDate = endDate;

        // 重新加载数据
        this.loadData();

        // 关闭弹窗
        DOM.$('.modal-overlay')?.remove();

        console.log('应用高级搜索:', { strategy, merge, startDate, endDate });
    }
    showColumnSettings() { console.log('显示列设置'); }
    exportData() { console.log('导出数据'); }

    // 动态操作方法
    viewTicket(id) { console.log('查看工单:', id); }
    editTicket(id) { console.log('编辑工单:', id); }
    submitTicket(id) { console.log('提交工单:', id); }
    deleteTicket(id) { console.log('删除工单:', id); }
    dispatchTicket(id) { console.log('派单:', id); }
    instantHandle(id) { console.log('即时办结:', id); }
    acceptTicket(id) { console.log('接收工单:', id); }
    rejectTicket(id) { console.log('退回工单:', id); }
    updateProgress(id) { console.log('更新进度:', id); }
    completeTicket(id) { console.log('办结工单:', id); }
    suspendTicket(id) { console.log('挂起工单:', id); }
    superviseTicket(id) { console.log('督办工单:', id); }
    coordinateTicket(id) { console.log('协调工单:', id); }
    approveTicket(id) { console.log('审核通过:', id); }
    returnTicket(id) { console.log('退回修改:', id); }
    startCallback(id) { console.log('开始回访:', id); }
    recordCallback(id) { console.log('记录回访结果:', id); }
    reopenTicket(id) { console.log('重新开启:', id); }
    restartTicket(id) { console.log('重启工单:', id); }
    mergeTicket(id) { console.log('合并工单:', id); }
    splitTicket(id) { console.log('拆分工单:', id); }
    vipHandle(id) { console.log('VIP专线处理:', id); }

    /**
     * 初始化角色定制化功能
     */
    initializeRoleCustomization() {
        if (!this.currentUser) return;

        // 根据角色定制筛选条件
        this.setupRoleSpecificFilters();

        // 根据角色定制默认排序
        this.setupRoleSpecificSorting();

        // 根据角色定制操作按钮
        this.setupRoleSpecificActions();

        // 根据角色定制列显示
        this.setupRoleSpecificColumns();
    }

    /**
     * 设置角色专用筛选条件
     */
    setupRoleSpecificFilters() {
        const role = this.currentUser.role;
        const filtersContainer = DOM.$('#roleSpecificFilters');

        if (!filtersContainer) return;

        let roleFilters = '';

        switch (role) {
            case 'municipal_operator':
                roleFilters = `
                    <div class="role-filter-group">
                        <label>处理策略：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="strategy" data-value="">全部</button>
                            <button class="filter-btn" data-filter="strategy" data-value="instant">可即时办结</button>
                            <button class="filter-btn" data-filter="strategy" data-value="dispatch">需要派单</button>
                            <button class="filter-btn" data-filter="strategy" data-value="complex">复杂协调</button>
                        </div>
                    </div>
                    <div class="role-filter-group">
                        <label>合并机会：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="merge" data-value="">全部</button>
                            <button class="filter-btn" data-filter="merge" data-value="similar">有相似工单</button>
                            <button class="filter-btn" data-filter="merge" data-value="mergeable">可合并工单</button>
                            <button class="filter-btn" data-filter="merge" data-value="independent">独立工单</button>
                        </div>
                    </div>
                `;
                break;

            case 'manager':
                roleFilters = `
                    <div class="role-filter-group">
                        <label>督办状态：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="supervision" data-value="">全部</button>
                            <button class="filter-btn" data-filter="supervision" data-value="none">无督办</button>
                            <button class="filter-btn" data-filter="supervision" data-value="general">一般督办</button>
                            <button class="filter-btn" data-filter="supervision" data-value="important">重点督办</button>
                            <button class="filter-btn" data-filter="supervision" data-value="leader">领导督办</button>
                        </div>
                    </div>
                    <div class="role-filter-group">
                        <label>异常工单：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="exception" data-value="">全部</button>
                            <button class="filter-btn" data-filter="exception" data-value="overtime">超时工单</button>
                            <button class="filter-btn" data-filter="exception" data-value="suspended">挂起工单</button>
                            <button class="filter-btn" data-filter="exception" data-value="restarted">重启工单</button>
                        </div>
                    </div>
                `;
                break;

            case 'executor':
                roleFilters = `
                    <div class="role-filter-group">
                        <label>任务类型：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="task" data-value="">全部</button>
                            <button class="filter-btn" data-filter="task" data-value="main">我的主办</button>
                            <button class="filter-btn" data-filter="task" data-value="assist">我的协办</button>
                            <button class="filter-btn" data-filter="task" data-value="execute">我的执行</button>
                        </div>
                    </div>
                    <div class="role-filter-group">
                        <label>处理阶段：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="stage" data-value="">全部</button>
                            <button class="filter-btn" data-filter="stage" data-value="pending">待接收</button>
                            <button class="filter-btn" data-filter="stage" data-value="processing">处理中</button>
                            <button class="filter-btn" data-filter="stage" data-value="submitting">待提交</button>
                        </div>
                    </div>
                `;
                break;

            case 'callback_staff':
                roleFilters = `
                    <div class="role-filter-group">
                        <label>回访状态：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="callback" data-value="">全部</button>
                            <button class="filter-btn" data-filter="callback" data-value="pending">待回访</button>
                            <button class="filter-btn" data-filter="callback" data-value="calling">回访中</button>
                            <button class="filter-btn" data-filter="callback" data-value="completed">已回访</button>
                            <button class="filter-btn" data-filter="callback" data-value="refused">拒绝回访</button>
                        </div>
                    </div>
                    <div class="role-filter-group">
                        <label>重启风险：</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="risk" data-value="">全部</button>
                            <button class="filter-btn" data-filter="risk" data-value="high">高风险</button>
                            <button class="filter-btn" data-filter="risk" data-value="medium">中风险</button>
                            <button class="filter-btn" data-filter="risk" data-value="low">低风险</button>
                        </div>
                    </div>
                `;
                break;
        }

        if (roleFilters) {
            filtersContainer.innerHTML = roleFilters;
            filtersContainer.style.display = 'block';
            this.bindRoleFilterEvents();
        }
    }

    /**
     * 绑定角色筛选事件
     */
    bindRoleFilterEvents() {
        const filterBtns = DOM.$$('#roleSpecificFilters .filter-btn');
        filterBtns.forEach(btn => {
            EventHandler.on(btn, 'click', (e) => {
                const filterType = e.target.dataset.filter;
                const filterValue = e.target.dataset.value;

                // 更新按钮状态
                const group = e.target.closest('.role-filter-group');
                group.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                // 应用筛选
                this.filters[filterType] = filterValue;
                this.loadData();
            });
        });
    }

    /**
     * 设置角色专用排序
     */
    setupRoleSpecificSorting() {
        const role = this.currentUser.role;

        switch (role) {
            case 'municipal_operator':
                // 市级话务员：紧急程度 > 即时办结机会 > 督办级别 > 创建时间
                this.defaultSort = [
                    { field: 'urgency', order: 'desc' },
                    { field: 'instantHandleOpportunity', order: 'desc' },
                    { field: 'supervisionLevel', order: 'desc' },
                    { field: 'createTime', order: 'desc' }
                ];
                break;

            case 'manager':
                // 管理者：督办工单 > 超时预警 > 审核任务 > 异常工单
                this.defaultSort = [
                    { field: 'supervisionLevel', order: 'desc' },
                    { field: 'overtimeStatus', order: 'desc' },
                    { field: 'status', order: 'asc' },
                    { field: 'createTime', order: 'desc' }
                ];
                break;

            case 'executor':
                // 执行人员：任务紧急度 > 时限压力 > 地理距离 > 任务类型
                this.defaultSort = [
                    { field: 'urgency', order: 'desc' },
                    { field: 'timeLimit', order: 'asc' },
                    { field: 'area', order: 'asc' },
                    { field: 'collaborationMode', order: 'desc' }
                ];
                break;

            case 'callback_staff':
                // 回访员：回访紧急度 > 回访时限 > 风险等级 > 市民类型
                this.defaultSort = [
                    { field: 'urgency', order: 'desc' },
                    { field: 'timeLimit', order: 'asc' },
                    { field: 'restartCount', order: 'desc' },
                    { field: 'vipLevel', order: 'desc' }
                ];
                break;
        }

        // 应用默认排序
        if (this.defaultSort && this.defaultSort.length > 0) {
            this.sortField = this.defaultSort[0].field;
            this.sortOrder = this.defaultSort[0].order;
        }
    }

    /**
     * 初始化智能化功能
     */
    initializeIntelligentFeatures() {
        // 智能推荐搜索
        this.initIntelligentSearch();

        // 智能批量操作
        this.initIntelligentBatchOperations();

        // 智能提醒
        this.initIntelligentNotifications();
    }

    /**
     * 初始化智能搜索
     */
    initIntelligentSearch() {
        const searchInput = DOM.$('#searchInput');
        if (!searchInput) return;

        // 添加智能搜索提示
        const intelligentSearchContainer = document.createElement('div');
        intelligentSearchContainer.className = 'intelligent-search-container';
        intelligentSearchContainer.innerHTML = `
            <div class="search-suggestions" id="searchSuggestions" style="display: none;">
                <div class="suggestion-group">
                    <div class="suggestion-title">🔍 智能推荐</div>
                    <div class="suggestion-items" id="intelligentSuggestions"></div>
                </div>
                <div class="suggestion-group">
                    <div class="suggestion-title">📋 相似工单</div>
                    <div class="suggestion-items" id="similarTickets"></div>
                </div>
                <div class="suggestion-group">
                    <div class="suggestion-title">⚠️ 异常工单</div>
                    <div class="suggestion-items" id="exceptionTickets"></div>
                </div>
            </div>
        `;

        searchInput.parentNode.appendChild(intelligentSearchContainer);

        // 绑定智能搜索事件
        let searchTimeout;
        EventHandler.on(searchInput, 'input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performIntelligentSearch(e.target.value);
            }, 300);
        });

        EventHandler.on(searchInput, 'focus', () => {
            this.showSearchSuggestions();
        });

        EventHandler.on(document, 'click', (e) => {
            if (!e.target.closest('.search-input-group')) {
                this.hideSearchSuggestions();
            }
        });
    }

    /**
     * 执行智能搜索
     */
    performIntelligentSearch(query) {
        if (!query || query.length < 2) {
            this.hideSearchSuggestions();
            return;
        }

        // 智能推荐
        const intelligentSuggestions = this.generateIntelligentSuggestions(query);

        // 相似工单
        const similarTickets = this.findSimilarTickets(query);

        // 异常工单
        const exceptionTickets = this.findExceptionTickets(query);

        this.updateSearchSuggestions(intelligentSuggestions, similarTickets, exceptionTickets);
        this.showSearchSuggestions();
    }

    /**
     * 生成智能推荐
     */
    generateIntelligentSuggestions(query) {
        const suggestions = [];

        // 基于用户角色的智能推荐
        const role = this.currentUser.role;

        if (role === 'municipal_operator') {
            if (query.includes('紧急') || query.includes('急')) {
                suggestions.push('紧急程度为特急的工单');
                suggestions.push('即将超时的紧急工单');
            }
            if (query.includes('督办')) {
                suggestions.push('需要督办的工单');
                suggestions.push('市级督办工单');
            }
            if (query.includes('合并')) {
                suggestions.push('可合并的相似工单');
                suggestions.push('重复投诉工单');
            }
        } else if (role === 'manager') {
            if (query.includes('超时')) {
                suggestions.push('已超时工单');
                suggestions.push('即将超时工单');
            }
            if (query.includes('异常')) {
                suggestions.push('异常状态工单');
                suggestions.push('重启次数过多的工单');
            }
        } else if (role === 'executor') {
            if (query.includes('我的')) {
                suggestions.push('我的待处理工单');
                suggestions.push('我的主办工单');
                suggestions.push('我的协办工单');
            }
        }

        return suggestions.slice(0, 3);
    }

    /**
     * 查找相似工单
     */
    findSimilarTickets(query) {
        const similar = [];

        // 模拟查找相似工单
        if (query.includes('噪音')) {
            similar.push('噪音扰民相关工单 (5个)');
            similar.push('建筑施工噪音工单 (3个)');
        }
        if (query.includes('停水')) {
            similar.push('停水相关工单 (8个)');
            similar.push('供水管道维修工单 (2个)');
        }
        if (query.includes('垃圾')) {
            similar.push('垃圾清运工单 (12个)');
            similar.push('垃圾分类问题工单 (4个)');
        }

        return similar.slice(0, 3);
    }

    /**
     * 查找异常工单
     */
    findExceptionTickets(query) {
        const exceptions = [];

        // 模拟查找异常工单
        if (query.includes('超时')) {
            exceptions.push('已超时工单 (15个)');
            exceptions.push('即将超时工单 (8个)');
        }
        if (query.includes('重启')) {
            exceptions.push('重启次数>2的工单 (6个)');
            exceptions.push('本月重启工单 (23个)');
        }
        if (query.includes('挂起')) {
            exceptions.push('挂起超过3天的工单 (4个)');
            exceptions.push('挂起状态工单 (11个)');
        }

        return exceptions.slice(0, 3);
    }

    /**
     * 更新搜索建议
     */
    updateSearchSuggestions(intelligent, similar, exception) {
        const intelligentContainer = DOM.$('#intelligentSuggestions');
        const similarContainer = DOM.$('#similarTickets');
        const exceptionContainer = DOM.$('#exceptionTickets');

        if (intelligentContainer) {
            intelligentContainer.innerHTML = intelligent.map(item =>
                `<div class="suggestion-item intelligent" onclick="ticketListController.applySuggestion('${item}')">${item}</div>`
            ).join('');
        }

        if (similarContainer) {
            similarContainer.innerHTML = similar.map(item =>
                `<div class="suggestion-item similar" onclick="ticketListController.applySuggestion('${item}')">${item}</div>`
            ).join('');
        }

        if (exceptionContainer) {
            exceptionContainer.innerHTML = exception.map(item =>
                `<div class="suggestion-item exception" onclick="ticketListController.applySuggestion('${item}')">${item}</div>`
            ).join('');
        }
    }

    /**
     * 显示搜索建议
     */
    showSearchSuggestions() {
        const suggestions = DOM.$('#searchSuggestions');
        if (suggestions) {
            suggestions.style.display = 'block';
        }
    }

    /**
     * 隐藏搜索建议
     */
    hideSearchSuggestions() {
        const suggestions = DOM.$('#searchSuggestions');
        if (suggestions) {
            suggestions.style.display = 'none';
        }
    }

    /**
     * 应用搜索建议
     */
    applySuggestion(suggestion) {
        const searchInput = DOM.$('#searchInput');
        if (searchInput) {
            searchInput.value = suggestion;
            this.filters.search = suggestion;
            this.loadData();
            this.hideSearchSuggestions();
        }
    }

    /**
     * 初始化智能批量操作
     */
    initIntelligentBatchOperations() {
        // 智能选择推荐
        this.addIntelligentSelectionButtons();

        // 批量操作风险控制
        this.setupBatchOperationRiskControl();
    }

    /**
     * 添加智能选择按钮
     */
    addIntelligentSelectionButtons() {
        const batchActions = DOM.$('.batch-actions');
        if (!batchActions) return;

        const intelligentButtons = document.createElement('div');
        intelligentButtons.className = 'intelligent-selection';
        intelligentButtons.innerHTML = `
            <button class="btn btn-sm btn-outline" onclick="ticketListController.selectUrgentTickets()">
                ⚡ 选择紧急工单
            </button>
            <button class="btn btn-sm btn-outline" onclick="ticketListController.selectOvertimeTickets()">
                ⏰ 选择超时工单
            </button>
            <button class="btn btn-sm btn-outline" onclick="ticketListController.selectSimilarTickets()">
                🔗 选择相似工单
            </button>
        `;

        batchActions.insertBefore(intelligentButtons, batchActions.firstChild);
    }

    /**
     * 智能选择紧急工单
     */
    selectUrgentTickets() {
        const urgentTickets = this.currentData.filter(ticket =>
            ticket.urgency === 'critical' || ticket.supervisionLevel !== '无'
        );

        this.tableComponent.selectTickets(urgentTickets.map(t => t.id));
        Message.success(`已选择 ${urgentTickets.length} 个紧急工单`);
    }

    /**
     * 智能选择超时工单
     */
    selectOvertimeTickets() {
        const overtimeTickets = this.currentData.filter(ticket =>
            ticket.overtimeStatus === '已超时' || ticket.overtimeStatus === '即将超时'
        );

        this.tableComponent.selectTickets(overtimeTickets.map(t => t.id));
        Message.success(`已选择 ${overtimeTickets.length} 个超时工单`);
    }

    /**
     * 智能选择相似工单
     */
    selectSimilarTickets() {
        // 简单的相似度算法，实际应该更复杂
        const selectedTickets = this.tableComponent.getSelectedTickets();
        if (selectedTickets.length === 0) {
            Message.warning('请先选择一个工单作为参考');
            return;
        }

        const referenceTicket = selectedTickets[0];
        const similarTickets = this.currentData.filter(ticket =>
            ticket.id !== referenceTicket.id &&
            (ticket.title.includes(referenceTicket.title.split(' ')[0]) ||
             ticket.department === referenceTicket.department ||
             ticket.area === referenceTicket.area)
        );

        this.tableComponent.selectTickets([...selectedTickets.map(t => t.id), ...similarTickets.map(t => t.id)]);
        Message.success(`已选择 ${similarTickets.length} 个相似工单`);
    }

    /**
     * 设置批量操作风险控制
     */
    setupBatchOperationRiskControl() {
        // 重写批量操作方法，添加风险控制
        const originalBatchDelete = this.batchDelete.bind(this);
        this.batchDelete = function() {
            const selectedTickets = this.tableComponent.getSelectedTickets();
            const riskLevel = this.assessBatchOperationRisk(selectedTickets, 'delete');

            if (riskLevel === 'high') {
                Message.error('高风险操作：包含VIP工单或督办工单，无法批量删除');
                return;
            } else if (riskLevel === 'medium') {
                if (!confirm('中风险操作：包含处理中的工单，确定要批量删除吗？')) {
                    return;
                }
            }

            originalBatchDelete();
        };
    }

    /**
     * 评估批量操作风险
     */
    assessBatchOperationRisk(tickets, operation) {
        let riskLevel = 'low';

        for (const ticket of tickets) {
            // 高风险条件
            if (ticket.vipLevel !== '无' ||
                ticket.supervisionLevel !== '无' ||
                ticket.status === 'processing') {
                riskLevel = 'high';
                break;
            }

            // 中风险条件
            if (ticket.urgency === 'critical' ||
                ticket.restartCount > 0 ||
                ticket.overtimeStatus === '即将超时') {
                riskLevel = 'medium';
            }
        }

        return riskLevel;
    }

    /**
     * 初始化智能提醒
     */
    initIntelligentNotifications() {
        // 定期检查需要提醒的工单
        setInterval(() => {
            this.checkIntelligentNotifications();
        }, 60000); // 每分钟检查一次

        // 立即执行一次
        this.checkIntelligentNotifications();
    }

    /**
     * 检查智能提醒
     */
    checkIntelligentNotifications() {
        if (!this.currentData) return;

        const role = this.currentUser.role;
        const notifications = [];

        // 根据角色生成不同的提醒
        switch (role) {
            case 'municipal_operator':
                // 即将超时的紧急工单
                const urgentOvertime = this.currentData.filter(ticket =>
                    ticket.urgency === 'critical' && ticket.overtimeStatus === '即将超时'
                );
                if (urgentOvertime.length > 0) {
                    notifications.push({
                        type: 'warning',
                        title: '紧急工单即将超时',
                        message: `有 ${urgentOvertime.length} 个紧急工单即将超时，请及时处理`,
                        action: () => this.filterUrgentOvertimeTickets()
                    });
                }

                // 可合并的工单
                const mergeable = this.currentData.filter(ticket => ticket.canMerge);
                if (mergeable.length >= 3) {
                    notifications.push({
                        type: 'info',
                        title: '发现可合并工单',
                        message: `发现 ${mergeable.length} 个可能重复的工单，建议合并处理`,
                        action: () => this.showMergeableTickets()
                    });
                }
                break;

            case 'manager':
                // 需要督办的工单
                const needSupervision = this.currentData.filter(ticket =>
                    ticket.overtimeStatus === '已超时' && ticket.supervisionLevel === '无'
                );
                if (needSupervision.length > 0) {
                    notifications.push({
                        type: 'error',
                        title: '工单需要督办',
                        message: `有 ${needSupervision.length} 个已超时工单需要督办`,
                        action: () => this.showSupervisionNeededTickets()
                    });
                }
                break;

            case 'executor':
                // 我的待处理工单
                const myPending = this.currentData.filter(ticket =>
                    ticket.status === 'pending' && ticket.department === this.currentUser.department
                );
                if (myPending.length > 5) {
                    notifications.push({
                        type: 'warning',
                        title: '待处理工单较多',
                        message: `您有 ${myPending.length} 个待处理工单，请及时处理`,
                        action: () => this.showMyPendingTickets()
                    });
                }
                break;

            case 'callback_staff':
                // 需要回访的工单
                const needCallback = this.currentData.filter(ticket =>
                    ticket.status === 'callback'
                );
                if (needCallback.length > 0) {
                    notifications.push({
                        type: 'info',
                        title: '待回访工单',
                        message: `有 ${needCallback.length} 个工单需要回访`,
                        action: () => this.showCallbackTickets()
                    });
                }
                break;
        }

        // 显示通知
        notifications.forEach(notification => {
            this.showIntelligentNotification(notification);
        });
    }

    /**
     * 显示智能通知
     */
    showIntelligentNotification(notification) {
        // 检查是否已经显示过相同的通知
        const notificationId = `${notification.type}-${notification.title}`;
        if (this.shownNotifications && this.shownNotifications.has(notificationId)) {
            return;
        }

        if (!this.shownNotifications) {
            this.shownNotifications = new Set();
        }
        this.shownNotifications.add(notificationId);

        // 创建通知元素
        const notificationElement = document.createElement('div');
        notificationElement.className = `intelligent-notification ${notification.type}`;
        notificationElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-actions">
                    <button class="btn btn-sm btn-primary" onclick="this.closest('.intelligent-notification').remove(); (${notification.action.toString()})()">
                        查看
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="this.closest('.intelligent-notification').remove()">
                        忽略
                    </button>
                </div>
            </div>
            <button class="notification-close" onclick="this.closest('.intelligent-notification').remove()">×</button>
        `;

        // 添加到页面
        let notificationContainer = DOM.$('#notificationContainer');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notificationContainer';
            notificationContainer.className = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        notificationContainer.appendChild(notificationElement);

        // 5秒后自动消失
        setTimeout(() => {
            if (notificationElement.parentNode) {
                notificationElement.remove();
            }
        }, 5000);
    }

    /**
     * 初始化个性化设置
     */
    initializePersonalization() {
        // 加载用户个性化设置
        this.loadPersonalSettings();

        // 初始化显示密度控制
        this.initDisplayDensityControl();

        // 初始化智能主题切换
        this.initIntelligentTheme();

        // 初始化工作效率优化设置
        this.initEfficiencySettings();

        // 初始化自定义快捷键
        this.initCustomShortcuts();
    }

    /**
     * 加载用户个性化设置
     */
    loadPersonalSettings() {
        const savedSettings = localStorage.getItem(`ticket-list-settings-${this.currentUser.id}`);

        this.personalSettings = savedSettings ? JSON.parse(savedSettings) : {
            displayDensity: 'normal', // compact, normal, comfortable
            theme: 'auto', // light, dark, auto
            autoRefresh: true,
            refreshInterval: 30000,
            defaultPageSize: 20,
            showNotifications: true,
            soundEnabled: false,
            shortcuts: {
                'Ctrl+R': 'refresh',
                'Ctrl+F': 'search',
                'Ctrl+A': 'selectAll',
                'Delete': 'batchDelete'
            },
            columnWidths: {},
            hiddenColumns: [],
            customFilters: []
        };

        // 应用设置
        this.applyPersonalSettings();
    }

    /**
     * 保存个性化设置
     */
    savePersonalSettings() {
        localStorage.setItem(
            `ticket-list-settings-${this.currentUser.id}`,
            JSON.stringify(this.personalSettings)
        );
    }

    /**
     * 应用个性化设置
     */
    applyPersonalSettings() {
        // 应用显示密度
        this.applyDisplayDensity(this.personalSettings.displayDensity);

        // 应用主题
        this.applyTheme(this.personalSettings.theme);

        // 应用自动刷新设置
        if (this.personalSettings.autoRefresh) {
            this.setRefreshInterval(this.personalSettings.refreshInterval);
        }

        // 应用分页设置
        this.pageSize = this.personalSettings.defaultPageSize;
    }

    /**
     * 初始化显示密度控制
     */
    initDisplayDensityControl() {
        // 在工具栏添加密度控制按钮
        const toolbar = DOM.$('.toolbar-right');
        if (!toolbar) return;

        const densityControl = document.createElement('div');
        densityControl.className = 'density-control';
        densityControl.innerHTML = `
            <div class="dropdown">
                <button class="btn btn-icon" title="显示密度" id="densityToggle">
                    📏
                </button>
                <div class="dropdown-menu" id="densityMenu">
                    <div class="dropdown-item ${this.personalSettings.displayDensity === 'compact' ? 'active' : ''}"
                         data-density="compact">
                        <span>紧凑</span>
                        <small>显示更多内容</small>
                    </div>
                    <div class="dropdown-item ${this.personalSettings.displayDensity === 'normal' ? 'active' : ''}"
                         data-density="normal">
                        <span>标准</span>
                        <small>平衡显示</small>
                    </div>
                    <div class="dropdown-item ${this.personalSettings.displayDensity === 'comfortable' ? 'active' : ''}"
                         data-density="comfortable">
                        <span>舒适</span>
                        <small>更大间距</small>
                    </div>
                </div>
            </div>
        `;

        toolbar.insertBefore(densityControl, toolbar.firstChild);

        // 绑定事件
        EventHandler.on(DOM.$('#densityToggle'), 'click', (e) => {
            e.stopPropagation();
            const menu = DOM.$('#densityMenu');
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        });

        EventHandler.on(DOM.$$('#densityMenu .dropdown-item'), 'click', (e) => {
            const density = e.currentTarget.dataset.density;
            this.setDisplayDensity(density);
            DOM.$('#densityMenu').style.display = 'none';
        });

        // 点击外部关闭菜单
        EventHandler.on(document, 'click', (e) => {
            if (!e.target.closest('.density-control')) {
                const menu = DOM.$('#densityMenu');
                if (menu) menu.style.display = 'none';
            }
        });
    }

    /**
     * 设置显示密度
     */
    setDisplayDensity(density) {
        this.personalSettings.displayDensity = density;
        this.applyDisplayDensity(density);
        this.savePersonalSettings();

        // 更新菜单状态
        DOM.$$('#densityMenu .dropdown-item').forEach(item => {
            item.classList.toggle('active', item.dataset.density === density);
        });

        Message.success(`已切换到${density === 'compact' ? '紧凑' : density === 'normal' ? '标准' : '舒适'}显示`);
    }

    /**
     * 应用显示密度
     */
    applyDisplayDensity(density) {
        const body = document.body;
        body.classList.remove('density-compact', 'density-normal', 'density-comfortable');
        body.classList.add(`density-${density}`);

        // 动态调整表格行高
        const table = DOM.$('#ticketTable');
        if (table) {
            table.classList.remove('compact', 'normal', 'comfortable');
            table.classList.add(density);
        }
    }

    /**
     * 初始化智能主题切换
     */
    initIntelligentTheme() {
        // 在工具栏添加主题切换按钮
        const toolbar = DOM.$('.toolbar-right');
        if (!toolbar) return;

        const themeControl = document.createElement('div');
        themeControl.className = 'theme-control';
        themeControl.innerHTML = `
            <button class="btn btn-icon" title="主题切换" id="themeToggle">
                ${this.getThemeIcon(this.personalSettings.theme)}
            </button>
        `;

        toolbar.insertBefore(themeControl, toolbar.firstChild);

        // 绑定主题切换事件
        EventHandler.on(DOM.$('#themeToggle'), 'click', () => {
            this.toggleTheme();
        });

        // 监听系统主题变化（自动模式）
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addListener(() => {
                if (this.personalSettings.theme === 'auto') {
                    this.applyTheme('auto');
                }
            });
        }

        // 根据时间智能切换主题
        this.setupIntelligentThemeSchedule();
    }

    /**
     * 获取主题图标
     */
    getThemeIcon(theme) {
        const icons = {
            'light': '☀️',
            'dark': '🌙',
            'auto': '🌓'
        };
        return icons[theme] || icons.auto;
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const themes = ['light', 'dark', 'auto'];
        const currentIndex = themes.indexOf(this.personalSettings.theme);
        const nextTheme = themes[(currentIndex + 1) % themes.length];

        this.personalSettings.theme = nextTheme;
        this.applyTheme(nextTheme);
        this.savePersonalSettings();

        // 更新按钮图标
        const themeToggle = DOM.$('#themeToggle');
        if (themeToggle) {
            themeToggle.innerHTML = this.getThemeIcon(nextTheme);
        }

        const themeNames = { light: '浅色', dark: '深色', auto: '自动' };
        Message.success(`已切换到${themeNames[nextTheme]}主题`);
    }

    /**
     * 应用主题
     */
    applyTheme(theme) {
        const body = document.body;
        body.classList.remove('theme-light', 'theme-dark');

        if (theme === 'auto') {
            // 根据系统偏好或时间自动选择
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            const hour = new Date().getHours();
            const isNightTime = hour < 7 || hour > 19;

            if (prefersDark || isNightTime) {
                body.classList.add('theme-dark');
            } else {
                body.classList.add('theme-light');
            }
        } else {
            body.classList.add(`theme-${theme}`);
        }
    }

    /**
     * 设置智能主题时间表
     */
    setupIntelligentThemeSchedule() {
        if (this.personalSettings.theme !== 'auto') return;

        // 每小时检查一次是否需要切换主题
        setInterval(() => {
            if (this.personalSettings.theme === 'auto') {
                this.applyTheme('auto');
            }
        }, 3600000); // 1小时
    }

    /**
     * 初始化工作效率优化设置
     */
    initEfficiencySettings() {
        // 智能预加载
        this.setupIntelligentPreloading();

        // 自动保存草稿
        this.setupAutoSaveDraft();

        // 智能提醒优化
        this.setupIntelligentReminders();
    }

    /**
     * 设置智能预加载
     */
    setupIntelligentPreloading() {
        // 预测用户可能查看的工单并预加载
        let preloadTimeout;

        EventHandler.on(DOM.$('#ticketTable'), 'mouseover', (e) => {
            const row = e.target.closest('tr');
            if (!row || !row.dataset.id) return;

            clearTimeout(preloadTimeout);
            preloadTimeout = setTimeout(() => {
                this.preloadTicketDetails(row.dataset.id);
            }, 500); // 鼠标悬停500ms后预加载
        });
    }

    /**
     * 预加载工单详情
     */
    preloadTicketDetails(ticketId) {
        // 模拟预加载工单详情
        if (!this.preloadedTickets) {
            this.preloadedTickets = new Map();
        }

        if (!this.preloadedTickets.has(ticketId)) {
            // 这里应该调用实际的API
            console.log('预加载工单详情:', ticketId);
            this.preloadedTickets.set(ticketId, { loaded: true, timestamp: Date.now() });
        }
    }

    /**
     * 设置自动保存草稿
     */
    setupAutoSaveDraft() {
        // 监听表单变化，自动保存草稿
        let autoSaveTimeout;

        EventHandler.on(document, 'input', (e) => {
            if (e.target.closest('.ticket-form')) {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    this.autoSaveFormDraft(e.target.closest('.ticket-form'));
                }, 2000); // 2秒后自动保存
            }
        });
    }

    /**
     * 自动保存表单草稿
     */
    autoSaveFormDraft(form) {
        if (!form) return;

        const formData = new FormData(form);
        const draftData = {};

        for (let [key, value] of formData.entries()) {
            draftData[key] = value;
        }

        const draftKey = `form-draft-${form.dataset.ticketId || 'new'}`;
        localStorage.setItem(draftKey, JSON.stringify({
            data: draftData,
            timestamp: Date.now()
        }));

        // 显示保存提示
        this.showAutoSaveIndicator();
    }

    /**
     * 显示自动保存指示器
     */
    showAutoSaveIndicator() {
        let indicator = DOM.$('#autoSaveIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'autoSaveIndicator';
            indicator.className = 'auto-save-indicator';
            document.body.appendChild(indicator);
        }

        indicator.textContent = '✓ 已自动保存';
        indicator.style.display = 'block';

        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }

    /**
     * 初始化自定义快捷键
     */
    initCustomShortcuts() {
        EventHandler.on(document, 'keydown', (e) => {
            const shortcut = this.getShortcutKey(e);
            const action = this.personalSettings.shortcuts[shortcut];

            if (action && !e.target.matches('input, textarea, select')) {
                e.preventDefault();
                this.executeShortcutAction(action);
            }
        });
    }

    /**
     * 获取快捷键字符串
     */
    getShortcutKey(e) {
        const keys = [];

        if (e.ctrlKey) keys.push('Ctrl');
        if (e.altKey) keys.push('Alt');
        if (e.shiftKey) keys.push('Shift');

        if (e.key !== 'Control' && e.key !== 'Alt' && e.key !== 'Shift') {
            keys.push(e.key);
        }

        return keys.join('+');
    }

    /**
     * 执行快捷键操作
     */
    executeShortcutAction(action) {
        switch (action) {
            case 'refresh':
                this.loadData();
                Message.success('已刷新数据');
                break;
            case 'search':
                const searchInput = DOM.$('#searchInput');
                if (searchInput) {
                    searchInput.focus();
                }
                break;
            case 'selectAll':
                this.tableComponent.selectAll();
                break;
            case 'batchDelete':
                const selectedTickets = this.tableComponent.getSelectedTickets();
                if (selectedTickets.length > 0) {
                    this.batchDelete();
                } else {
                    Message.warning('请先选择要删除的工单');
                }
                break;
            default:
                console.log('未知快捷键操作:', action);
        }
    }

    /**
     * 智能排序算法
     */
    initIntelligentSorting() {
        // 初始化排序权重配置
        this.sortingWeights = {
            urgency: { critical: 100, urgent: 70, normal: 30 },
            supervision: { '市级督办': 90, '区级督办': 70, '街镇督办': 50, '无': 0 },
            overtime: { '已超时': 80, '即将超时': 60, '正常': 0 },
            vip: { '人大代表': 95, '政协委员': 90, '媒体记者': 85, '企业高管': 70, '无': 0 },
            restart: (count) => Math.min(count * 20, 60),
            timeRemaining: (remaining) => Math.max(0, 100 - (remaining / (24 * 60 * 60 * 1000)) * 100),
            satisfaction: (score) => score ? (5 - score) * 15 : 0
        };

        // 用户行为学习权重
        this.userBehaviorWeights = this.loadUserBehaviorWeights();
    }

    /**
     * 加载用户行为权重
     */
    loadUserBehaviorWeights() {
        const saved = localStorage.getItem(`user-behavior-weights-${this.currentUser.id}`);
        return saved ? JSON.parse(saved) : {
            clickFrequency: {},
            actionFrequency: {},
            timeSpent: {},
            lastUpdated: Date.now()
        };
    }

    /**
     * 保存用户行为权重
     */
    saveUserBehaviorWeights() {
        localStorage.setItem(
            `user-behavior-weights-${this.currentUser.id}`,
            JSON.stringify(this.userBehaviorWeights)
        );
    }

    /**
     * 智能排序数据
     */
    intelligentSort(data, sortField = null, sortOrder = null) {
        if (!data || data.length === 0) return data;

        // 如果指定了排序字段，使用传统排序
        if (sortField && sortOrder) {
            return this.traditionalSort(data, sortField, sortOrder);
        }

        // 使用智能排序算法
        return data.sort((a, b) => {
            const scoreA = this.calculateIntelligentScore(a);
            const scoreB = this.calculateIntelligentScore(b);

            // 分数相同时，按创建时间倒序
            if (scoreA === scoreB) {
                return new Date(b.createTime) - new Date(a.createTime);
            }

            return scoreB - scoreA; // 分数高的排在前面
        });
    }

    /**
     * 计算智能排序分数
     */
    calculateIntelligentScore(ticket) {
        let score = 0;

        // 基础权重分数
        score += this.sortingWeights.urgency[ticket.urgency] || 0;
        score += this.sortingWeights.supervision[ticket.supervisionLevel] || 0;
        score += this.sortingWeights.overtime[ticket.overtimeStatus] || 0;
        score += this.sortingWeights.vip[ticket.vipLevel] || 0;
        score += this.sortingWeights.restart(ticket.restartCount || 0);

        // 时间紧迫性分数
        const timeRemaining = ticket.timeLimit - Date.now();
        score += this.sortingWeights.timeRemaining(timeRemaining);

        // 满意度风险分数
        score += this.sortingWeights.satisfaction(ticket.satisfaction);

        // 用户行为学习分数
        score += this.calculateUserBehaviorScore(ticket);

        // 角色专用权重调整
        score = this.adjustScoreByRole(score, ticket);

        // 时间衰减因子（越久的工单分数越高）
        const ageHours = (Date.now() - ticket.createTime) / (1000 * 60 * 60);
        score += Math.min(ageHours * 2, 50);

        return Math.round(score);
    }

    /**
     * 计算用户行为分数
     */
    calculateUserBehaviorScore(ticket) {
        let behaviorScore = 0;

        // 点击频率权重
        const clickCount = this.userBehaviorWeights.clickFrequency[ticket.department] || 0;
        behaviorScore += Math.min(clickCount * 2, 20);

        // 操作频率权重
        const actionCount = this.userBehaviorWeights.actionFrequency[ticket.type] || 0;
        behaviorScore += Math.min(actionCount * 1.5, 15);

        // 处理时间权重（用户经常处理的类型优先级更高）
        const avgTime = this.userBehaviorWeights.timeSpent[ticket.type] || 0;
        if (avgTime > 0) {
            behaviorScore += Math.min(10 / (avgTime / 60000), 10); // 处理时间越短，优先级越高
        }

        return behaviorScore;
    }

    /**
     * 根据角色调整分数
     */
    adjustScoreByRole(score, ticket) {
        const role = this.currentUser.role;

        switch (role) {
            case 'municipal_operator':
                // 话务员更关注即时办结和派单
                if (ticket.instantHandleOpportunity === '可即时办结') {
                    score += 30;
                }
                if (ticket.status === 'pending') {
                    score += 20;
                }
                break;

            case 'manager':
                // 管理者更关注督办和异常
                if (ticket.supervisionLevel !== '无') {
                    score += 40;
                }
                if (ticket.overtimeStatus === '已超时') {
                    score += 35;
                }
                break;

            case 'executor':
                // 执行者更关注自己负责的工单
                if (ticket.department === this.currentUser.department) {
                    score += 50;
                }
                if (ticket.status === 'processing') {
                    score += 25;
                }
                break;

            case 'callback_staff':
                // 回访员更关注回访相关
                if (ticket.status === 'callback') {
                    score += 60;
                }
                if (ticket.satisfaction && ticket.satisfaction < 3) {
                    score += 40;
                }
                break;
        }

        return score;
    }

    /**
     * 传统排序（用户手动指定字段时）
     */
    traditionalSort(data, field, order) {
        return data.sort((a, b) => {
            let valueA = this.getSortValue(a, field);
            let valueB = this.getSortValue(b, field);

            // 处理不同数据类型
            if (typeof valueA === 'string') {
                valueA = valueA.toLowerCase();
                valueB = valueB.toLowerCase();
            }

            let result = 0;
            if (valueA < valueB) result = -1;
            else if (valueA > valueB) result = 1;

            return order === 'desc' ? -result : result;
        });
    }

    /**
     * 获取排序值
     */
    getSortValue(item, field) {
        switch (field) {
            case 'urgency':
                const urgencyOrder = { normal: 1, urgent: 2, critical: 3 };
                return urgencyOrder[item.urgency] || 0;
            case 'supervisionLevel':
                const supervisionOrder = { '无': 0, '街镇督办': 1, '区级督办': 2, '市级督办': 3 };
                return supervisionOrder[item.supervisionLevel] || 0;
            case 'overtimeStatus':
                const overtimeOrder = { '正常': 0, '即将超时': 1, '已超时': 2 };
                return overtimeOrder[item.overtimeStatus] || 0;
            case 'createTime':
            case 'timeLimit':
                return new Date(item[field]).getTime();
            default:
                return item[field] || '';
        }
    }

    /**
     * 记录用户行为
     */
    recordUserBehavior(action, ticket) {
        const now = Date.now();

        switch (action) {
            case 'click':
                this.userBehaviorWeights.clickFrequency[ticket.department] =
                    (this.userBehaviorWeights.clickFrequency[ticket.department] || 0) + 1;
                break;

            case 'action':
                this.userBehaviorWeights.actionFrequency[ticket.type] =
                    (this.userBehaviorWeights.actionFrequency[ticket.type] || 0) + 1;
                break;

            case 'timeSpent':
                if (!this.userBehaviorWeights.timeSpent[ticket.type]) {
                    this.userBehaviorWeights.timeSpent[ticket.type] = [];
                }
                this.userBehaviorWeights.timeSpent[ticket.type].push(now);
                break;
        }

        this.userBehaviorWeights.lastUpdated = now;

        // 定期保存（避免频繁写入）
        if (!this.behaviorSaveTimeout) {
            this.behaviorSaveTimeout = setTimeout(() => {
                this.saveUserBehaviorWeights();
                this.behaviorSaveTimeout = null;
            }, 5000);
        }
    }

    /**
     * 动态调整排序权重
     */
    adjustSortingWeights() {
        // 根据用户行为和系统反馈动态调整权重
        const recentBehavior = this.analyzeRecentBehavior();

        if (recentBehavior.focusOnUrgent) {
            this.sortingWeights.urgency.critical += 10;
            this.sortingWeights.urgency.urgent += 5;
        }

        if (recentBehavior.focusOnSupervision) {
            this.sortingWeights.supervision['市级督办'] += 15;
            this.sortingWeights.supervision['区级督办'] += 10;
        }

        if (recentBehavior.focusOnOvertime) {
            this.sortingWeights.overtime['已超时'] += 20;
            this.sortingWeights.overtime['即将超时'] += 10;
        }
    }

    /**
     * 分析最近用户行为
     */
    analyzeRecentBehavior() {
        const recentActions = this.getRecentActions(24 * 60 * 60 * 1000); // 最近24小时

        return {
            focusOnUrgent: recentActions.filter(a => a.urgency === 'critical').length > 5,
            focusOnSupervision: recentActions.filter(a => a.supervisionLevel !== '无').length > 3,
            focusOnOvertime: recentActions.filter(a => a.overtimeStatus !== '正常').length > 4
        };
    }

    /**
     * 获取最近操作记录
     */
    getRecentActions(timeRange) {
        // 这里应该从实际的操作日志中获取数据
        // 现在返回模拟数据
        return [];
    }

    /**
     * 增强批量操作智能化
     */
    enhanceBatchOperations() {
        // 智能批量分组
        this.initIntelligentBatchGrouping();

        // 批量操作预测
        this.initBatchOperationPrediction();

        // 批量操作优化建议
        this.initBatchOptimizationSuggestions();
    }

    /**
     * 初始化智能批量分组
     */
    initIntelligentBatchGrouping() {
        // 在批量操作工具栏添加智能分组按钮
        const batchActions = DOM.$('.batch-actions');
        if (!batchActions) return;

        const groupingButtons = document.createElement('div');
        groupingButtons.className = 'intelligent-grouping';
        groupingButtons.innerHTML = `
            <div class="dropdown">
                <button class="btn btn-sm btn-outline" id="intelligentGrouping">
                    🧠 智能分组
                </button>
                <div class="dropdown-menu" id="groupingMenu">
                    <div class="dropdown-item" data-group="department">
                        <span>按承办单位分组</span>
                        <small>相同单位一起处理</small>
                    </div>
                    <div class="dropdown-item" data-group="area">
                        <span>按区域分组</span>
                        <small>相同区域一起处理</small>
                    </div>
                    <div class="dropdown-item" data-group="type">
                        <span>按问题类型分组</span>
                        <small>相似问题一起处理</small>
                    </div>
                    <div class="dropdown-item" data-group="urgency">
                        <span>按紧急程度分组</span>
                        <small>相同优先级一起处理</small>
                    </div>
                    <div class="dropdown-item" data-group="similarity">
                        <span>按相似度分组</span>
                        <small>AI识别相似工单</small>
                    </div>
                </div>
            </div>
        `;

        batchActions.appendChild(groupingButtons);

        // 绑定事件
        EventHandler.on(DOM.$('#intelligentGrouping'), 'click', (e) => {
            e.stopPropagation();
            const menu = DOM.$('#groupingMenu');
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        });

        EventHandler.on(DOM.$$('#groupingMenu .dropdown-item'), 'click', (e) => {
            const groupType = e.currentTarget.dataset.group;
            this.performIntelligentGrouping(groupType);
            DOM.$('#groupingMenu').style.display = 'none';
        });
    }

    /**
     * 执行智能分组
     */
    performIntelligentGrouping(groupType) {
        const selectedTickets = this.tableComponent.getSelectedTickets();
        if (selectedTickets.length < 2) {
            Message.warning('请至少选择2个工单进行分组');
            return;
        }

        const groups = this.groupTickets(selectedTickets, groupType);
        this.showGroupingResults(groups, groupType);
    }

    /**
     * 分组工单
     */
    groupTickets(tickets, groupType) {
        const groups = {};

        tickets.forEach(ticket => {
            let groupKey;

            switch (groupType) {
                case 'department':
                    groupKey = ticket.department;
                    break;
                case 'area':
                    groupKey = ticket.area;
                    break;
                case 'type':
                    groupKey = ticket.type;
                    break;
                case 'urgency':
                    groupKey = ticket.urgency;
                    break;
                case 'similarity':
                    groupKey = this.calculateSimilarityGroup(ticket, tickets);
                    break;
                default:
                    groupKey = 'default';
            }

            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(ticket);
        });

        return groups;
    }

    /**
     * 计算相似度分组
     */
    calculateSimilarityGroup(ticket, allTickets) {
        // 简化的相似度算法
        const keywords = ticket.title.split(' ').filter(word => word.length > 2);

        for (const otherTicket of allTickets) {
            if (otherTicket.id === ticket.id) continue;

            const otherKeywords = otherTicket.title.split(' ').filter(word => word.length > 2);
            const commonKeywords = keywords.filter(word => otherKeywords.includes(word));

            if (commonKeywords.length >= 2) {
                return `相似组-${commonKeywords[0]}`;
            }
        }

        return `独立组-${ticket.id}`;
    }

    /**
     * 显示分组结果
     */
    showGroupingResults(groups, groupType) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content grouping-results-modal">
                <div class="modal-header">
                    <h3>智能分组结果</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="grouping-summary">
                        <p>按${this.getGroupTypeName(groupType)}分组，共分为 ${Object.keys(groups).length} 组：</p>
                    </div>
                    <div class="groups-container">
                        ${Object.entries(groups).map(([groupName, tickets]) => `
                            <div class="group-item">
                                <div class="group-header">
                                    <h4>${groupName} (${tickets.length}个工单)</h4>
                                    <div class="group-actions">
                                        <button class="btn btn-sm btn-primary" onclick="ticketListController.batchProcessGroup('${groupName}', ${JSON.stringify(tickets.map(t => t.id))})">
                                            批量处理
                                        </button>
                                        <button class="btn btn-sm btn-outline" onclick="ticketListController.selectGroup(${JSON.stringify(tickets.map(t => t.id))})">
                                            选择此组
                                        </button>
                                    </div>
                                </div>
                                <div class="group-tickets">
                                    ${tickets.map(ticket => `
                                        <div class="group-ticket-item">
                                            <span class="ticket-number">${ticket.ticketNumber}</span>
                                            <span class="ticket-title">${ticket.title}</span>
                                            <span class="ticket-status">${this.getStatusText(ticket.status)}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">关闭</button>
                    <button class="btn btn-primary" onclick="ticketListController.applyOptimalBatchStrategy(${JSON.stringify(groups)})">
                        应用最优处理策略
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * 获取分组类型名称
     */
    getGroupTypeName(groupType) {
        const names = {
            department: '承办单位',
            area: '区域',
            type: '问题类型',
            urgency: '紧急程度',
            similarity: '相似度'
        };
        return names[groupType] || groupType;
    }

    /**
     * 批量处理分组
     */
    batchProcessGroup(groupName, ticketIds) {
        console.log('批量处理分组:', groupName, ticketIds);

        // 根据分组特点推荐最佳处理方式
        const recommendations = this.getBatchProcessRecommendations(groupName, ticketIds);
        this.showBatchProcessRecommendations(recommendations);
    }

    /**
     * 获取批量处理建议
     */
    getBatchProcessRecommendations(groupName, ticketIds) {
        const tickets = ticketIds.map(id => this.currentData.find(t => t.id === id)).filter(Boolean);

        const recommendations = [];

        // 分析工单特征
        const hasUrgent = tickets.some(t => t.urgency === 'critical');
        const hasSupervision = tickets.some(t => t.supervisionLevel !== '无');
        const hasVIP = tickets.some(t => t.vipLevel !== '无');
        const sameArea = tickets.every(t => t.area === tickets[0].area);
        const sameDepartment = tickets.every(t => t.department === tickets[0].department);

        // 生成建议
        if (hasUrgent) {
            recommendations.push({
                type: 'warning',
                title: '紧急处理建议',
                content: '该组包含紧急工单，建议优先处理',
                action: 'prioritize'
            });
        }

        if (hasSupervision) {
            recommendations.push({
                type: 'info',
                title: '督办流程建议',
                content: '该组包含督办工单，需要特殊流程处理',
                action: 'supervision_process'
            });
        }

        if (hasVIP) {
            recommendations.push({
                type: 'warning',
                title: 'VIP客户建议',
                content: '该组包含VIP客户工单，建议专人处理',
                action: 'vip_process'
            });
        }

        if (sameArea && sameDepartment) {
            recommendations.push({
                type: 'success',
                title: '统一处理建议',
                content: '该组工单区域和部门相同，可以统一派单处理',
                action: 'unified_dispatch'
            });
        }

        if (tickets.length >= 5) {
            recommendations.push({
                type: 'info',
                title: '批量优化建议',
                content: '工单数量较多，建议分批处理以提高效率',
                action: 'batch_optimize'
            });
        }

        return recommendations;
    }

    /**
     * 显示批量处理建议
     */
    showBatchProcessRecommendations(recommendations) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content batch-recommendations-modal">
                <div class="modal-header">
                    <h3>🧠 智能处理建议</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="recommendations-list">
                        ${recommendations.map(rec => `
                            <div class="recommendation-item ${rec.type}">
                                <div class="recommendation-icon">
                                    ${rec.type === 'warning' ? '⚠️' : rec.type === 'info' ? 'ℹ️' : '✅'}
                                </div>
                                <div class="recommendation-content">
                                    <h4>${rec.title}</h4>
                                    <p>${rec.content}</p>
                                </div>
                                <div class="recommendation-action">
                                    <button class="btn btn-sm btn-primary" onclick="ticketListController.applyRecommendation('${rec.action}')">
                                        应用建议
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="ticketListController.applyAllRecommendations(${JSON.stringify(recommendations)})">
                        应用所有建议
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * 应用处理建议
     */
    applyRecommendation(action) {
        switch (action) {
            case 'prioritize':
                Message.success('已将紧急工单设置为最高优先级');
                break;
            case 'supervision_process':
                Message.success('已启动督办流程');
                break;
            case 'vip_process':
                Message.success('已分配VIP专员处理');
                break;
            case 'unified_dispatch':
                Message.success('已统一派单处理');
                break;
            case 'batch_optimize':
                Message.success('已优化批量处理顺序');
                break;
            default:
                console.log('应用建议:', action);
        }

        // 关闭模态框
        DOM.$('.modal-overlay')?.remove();
    }

    /**
     * 选择分组
     */
    selectGroup(ticketIds) {
        this.tableComponent.selectTickets(ticketIds);
        DOM.$('.modal-overlay')?.remove();
        Message.success(`已选择 ${ticketIds.length} 个工单`);
    }

    /**
     * 应用最优批量策略
     */
    applyOptimalBatchStrategy(groups) {
        // 分析所有分组，制定最优处理策略
        const strategy = this.calculateOptimalStrategy(groups);
        this.executeOptimalStrategy(strategy);

        DOM.$('.modal-overlay')?.remove();
        Message.success('已应用最优批量处理策略');
    }

    /**
     * 计算最优策略
     */
    calculateOptimalStrategy(groups) {
        const strategy = {
            processingOrder: [],
            parallelGroups: [],
            specialHandling: []
        };

        // 按优先级排序分组
        const sortedGroups = Object.entries(groups).sort(([, ticketsA], [, ticketsB]) => {
            const priorityA = this.calculateGroupPriority(ticketsA);
            const priorityB = this.calculateGroupPriority(ticketsB);
            return priorityB - priorityA;
        });

        sortedGroups.forEach(([groupName, tickets]) => {
            const groupPriority = this.calculateGroupPriority(tickets);

            if (groupPriority > 80) {
                strategy.specialHandling.push({ groupName, tickets, reason: '高优先级' });
            } else if (groupPriority > 50) {
                strategy.processingOrder.push({ groupName, tickets, priority: 'high' });
            } else {
                strategy.parallelGroups.push({ groupName, tickets, priority: 'normal' });
            }
        });

        return strategy;
    }

    /**
     * 计算分组优先级
     */
    calculateGroupPriority(tickets) {
        let priority = 0;

        tickets.forEach(ticket => {
            if (ticket.urgency === 'critical') priority += 30;
            if (ticket.supervisionLevel !== '无') priority += 25;
            if (ticket.vipLevel !== '无') priority += 20;
            if (ticket.overtimeStatus === '已超时') priority += 15;
            if (ticket.restartCount > 0) priority += 10;
        });

        return priority / tickets.length; // 平均优先级
    }

    /**
     * 执行最优策略
     */
    executeOptimalStrategy(strategy) {
        console.log('执行最优批量处理策略:', strategy);

        // 这里应该实现具体的策略执行逻辑
        // 比如：
        // 1. 特殊处理组立即处理
        // 2. 高优先级组按顺序处理
        // 3. 普通组并行处理

        // 显示策略执行结果
        this.showStrategyExecutionResult(strategy);
    }

    /**
     * 显示策略执行结果
     */
    showStrategyExecutionResult(strategy) {
        const notification = {
            type: 'success',
            title: '批量处理策略已应用',
            message: `特殊处理: ${strategy.specialHandling.length}组, 优先处理: ${strategy.processingOrder.length}组, 并行处理: ${strategy.parallelGroups.length}组`,
            action: () => console.log('查看处理进度')
        };

        this.showIntelligentNotification(notification);
    }

    /**
     * 初始化实时协作功能
     */
    initRealTimeCollaboration() {
        // 初始化WebSocket连接
        this.initWebSocketConnection();

        // 初始化在线状态显示
        this.initOnlineStatusDisplay();

        // 初始化协同工作功能
        this.initCollaborativeFeatures();

        // 初始化实时通知系统
        this.initRealTimeNotifications();
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocketConnection() {
        // 模拟WebSocket连接
        this.wsConnection = {
            connected: false,
            reconnectAttempts: 0,
            maxReconnectAttempts: 5,
            reconnectInterval: 5000
        };

        this.connectWebSocket();
    }

    /**
     * 连接WebSocket
     */
    connectWebSocket() {
        try {
            // 这里应该是实际的WebSocket连接
            // this.ws = new WebSocket('ws://localhost:8080/ticket-updates');

            // 模拟连接成功
            setTimeout(() => {
                this.wsConnection.connected = true;
                this.wsConnection.reconnectAttempts = 0;

                console.log('WebSocket连接已建立');
                this.showConnectionStatus('connected');

                // 开始模拟实时更新
                this.startMockRealTimeUpdates();

            }, 1000);

        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.handleConnectionError();
        }
    }

    /**
     * 处理连接错误
     */
    handleConnectionError() {
        this.wsConnection.connected = false;
        this.showConnectionStatus('disconnected');

        if (this.wsConnection.reconnectAttempts < this.wsConnection.maxReconnectAttempts) {
            this.wsConnection.reconnectAttempts++;

            setTimeout(() => {
                console.log(`尝试重连 (${this.wsConnection.reconnectAttempts}/${this.wsConnection.maxReconnectAttempts})`);
                this.connectWebSocket();
            }, this.wsConnection.reconnectInterval);
        } else {
            Message.error('实时连接失败，请刷新页面重试');
        }
    }

    /**
     * 显示连接状态
     */
    showConnectionStatus(status) {
        let statusIndicator = DOM.$('#connectionStatus');
        if (!statusIndicator) {
            statusIndicator = document.createElement('div');
            statusIndicator.id = 'connectionStatus';
            statusIndicator.className = 'connection-status';
            document.body.appendChild(statusIndicator);
        }

        const statusConfig = {
            connected: { icon: '🟢', text: '实时连接已建立', class: 'connected' },
            disconnected: { icon: '🔴', text: '实时连接已断开', class: 'disconnected' },
            connecting: { icon: '🟡', text: '正在连接...', class: 'connecting' }
        };

        const config = statusConfig[status];
        statusIndicator.innerHTML = `${config.icon} ${config.text}`;
        statusIndicator.className = `connection-status ${config.class}`;

        // 3秒后隐藏状态指示器（连接成功时）
        if (status === 'connected') {
            setTimeout(() => {
                statusIndicator.style.display = 'none';
            }, 3000);
        }
    }

    /**
     * 开始模拟实时更新
     */
    startMockRealTimeUpdates() {
        // 模拟接收实时更新
        setInterval(() => {
            if (this.wsConnection.connected && Math.random() < 0.3) {
                this.simulateRealTimeUpdate();
            }
        }, 10000); // 每10秒可能收到一次更新
    }

    /**
     * 模拟实时更新
     */
    simulateRealTimeUpdate() {
        const updateTypes = ['status_change', 'new_ticket', 'assignment_change', 'comment_added'];
        const updateType = updateTypes[Math.floor(Math.random() * updateTypes.length)];

        const mockUpdate = {
            type: updateType,
            ticketId: this.currentData[Math.floor(Math.random() * this.currentData.length)]?.id,
            userId: 'user_' + Math.floor(Math.random() * 100),
            userName: '张三',
            timestamp: Date.now(),
            data: {}
        };

        switch (updateType) {
            case 'status_change':
                mockUpdate.data = {
                    oldStatus: 'processing',
                    newStatus: 'review',
                    reason: '处理完成，提交审核'
                };
                break;
            case 'new_ticket':
                mockUpdate.data = {
                    ticketNumber: 'WD' + Date.now(),
                    title: '新的市民投诉',
                    urgency: 'normal'
                };
                break;
            case 'assignment_change':
                mockUpdate.data = {
                    oldAssignee: '李四',
                    newAssignee: '王五',
                    reason: '工作调整'
                };
                break;
            case 'comment_added':
                mockUpdate.data = {
                    comment: '已联系市民，问题正在处理中',
                    isInternal: false
                };
                break;
        }

        this.handleRealTimeUpdate(mockUpdate);
    }

    /**
     * 处理实时更新
     */
    handleRealTimeUpdate(update) {
        console.log('收到实时更新:', update);

        switch (update.type) {
            case 'status_change':
                this.handleStatusChange(update);
                break;
            case 'new_ticket':
                this.handleNewTicket(update);
                break;
            case 'assignment_change':
                this.handleAssignmentChange(update);
                break;
            case 'comment_added':
                this.handleCommentAdded(update);
                break;
        }

        // 显示实时更新通知
        this.showRealTimeNotification(update);
    }

    /**
     * 处理状态变更
     */
    handleStatusChange(update) {
        const ticket = this.currentData.find(t => t.id === update.ticketId);
        if (ticket) {
            ticket.status = update.data.newStatus;

            // 更新表格中的对应行
            this.updateTableRow(ticket);

            // 如果当前用户正在查看这个工单，显示提醒
            if (this.currentViewingTicket === update.ticketId) {
                this.showCollaborationAlert('工单状态已更新', `${update.userName} 将工单状态更改为 ${this.getStatusText(update.data.newStatus)}`);
            }
        }
    }

    /**
     * 处理新工单
     */
    handleNewTicket(update) {
        // 如果符合当前筛选条件，添加到列表
        if (this.shouldShowNewTicket(update.data)) {
            // 重新加载数据以包含新工单
            this.loadData();

            // 显示新工单提醒
            this.showNewTicketAlert(update.data);
        }
    }

    /**
     * 处理分配变更
     */
    handleAssignmentChange(update) {
        const ticket = this.currentData.find(t => t.id === update.ticketId);
        if (ticket) {
            ticket.currentHandler = update.data.newAssignee;
            this.updateTableRow(ticket);

            // 如果分配给当前用户，显示特殊提醒
            if (update.data.newAssignee === this.currentUser.name) {
                this.showAssignmentAlert(ticket, '新工单分配', `${update.userName} 将工单分配给您`);
            }
        }
    }

    /**
     * 处理评论添加
     */
    handleCommentAdded(update) {
        const ticket = this.currentData.find(t => t.id === update.ticketId);
        if (ticket) {
            // 更新评论计数或其他相关信息
            ticket.commentCount = (ticket.commentCount || 0) + 1;

            // 如果当前用户关注这个工单，显示提醒
            if (this.isUserWatchingTicket(update.ticketId)) {
                this.showCommentAlert(ticket, update.data.comment, update.userName);
            }
        }
    }

    /**
     * 更新表格行
     */
    updateTableRow(ticket) {
        const row = DOM.$(`tr[data-id="${ticket.id}"]`);
        if (row && this.tableComponent) {
            // 重新渲染这一行
            const newRowHtml = this.tableComponent.renderRow(ticket);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newRowHtml;
            const newRow = tempDiv.firstChild;

            // 添加更新动画
            row.classList.add('updating');
            setTimeout(() => {
                row.parentNode.replaceChild(newRow, row);
                newRow.classList.add('updated');
                setTimeout(() => {
                    newRow.classList.remove('updated');
                }, 2000);
            }, 300);
        }
    }

    /**
     * 显示实时更新通知
     */
    showRealTimeNotification(update) {
        const notification = {
            type: 'info',
            title: this.getRealTimeNotificationTitle(update.type),
            message: this.getRealTimeNotificationMessage(update),
            action: () => this.handleNotificationAction(update)
        };

        this.showIntelligentNotification(notification);
    }

    /**
     * 获取实时通知标题
     */
    getRealTimeNotificationTitle(type) {
        const titles = {
            status_change: '工单状态更新',
            new_ticket: '新工单创建',
            assignment_change: '工单分配变更',
            comment_added: '新评论添加'
        };
        return titles[type] || '实时更新';
    }

    /**
     * 获取实时通知消息
     */
    getRealTimeNotificationMessage(update) {
        switch (update.type) {
            case 'status_change':
                return `${update.userName} 更新了工单状态为 ${this.getStatusText(update.data.newStatus)}`;
            case 'new_ticket':
                return `新工单 ${update.data.ticketNumber} 已创建`;
            case 'assignment_change':
                return `工单已从 ${update.data.oldAssignee} 重新分配给 ${update.data.newAssignee}`;
            case 'comment_added':
                return `${update.userName} 添加了新评论`;
            default:
                return '收到实时更新';
        }
    }

    /**
     * 初始化在线状态显示
     */
    initOnlineStatusDisplay() {
        // 在用户信息区域显示在线状态
        this.updateOnlineUsers();

        // 定期更新在线用户列表
        setInterval(() => {
            this.updateOnlineUsers();
        }, 30000); // 每30秒更新一次
    }

    /**
     * 更新在线用户
     */
    updateOnlineUsers() {
        // 模拟在线用户数据
        const onlineUsers = [
            { id: 1, name: '张三', role: 'municipal_operator', status: 'online', avatar: '👨‍💼' },
            { id: 2, name: '李四', role: 'executor', status: 'busy', avatar: '👩‍💻' },
            { id: 3, name: '王五', role: 'manager', status: 'away', avatar: '👨‍💼' },
            { id: 4, name: '赵六', role: 'callback_staff', status: 'online', avatar: '👩‍💼' }
        ];

        this.displayOnlineUsers(onlineUsers);
    }

    /**
     * 显示在线用户
     */
    displayOnlineUsers(users) {
        let onlinePanel = DOM.$('#onlineUsersPanel');
        if (!onlinePanel) {
            onlinePanel = document.createElement('div');
            onlinePanel.id = 'onlineUsersPanel';
            onlinePanel.className = 'online-users-panel';
            onlinePanel.innerHTML = `
                <div class="panel-header">
                    <span>在线用户 (${users.length})</span>
                    <button class="panel-toggle" onclick="this.closest('.online-users-panel').classList.toggle('collapsed')">
                        ▼
                    </button>
                </div>
                <div class="panel-content" id="onlineUsersList"></div>
            `;
            document.body.appendChild(onlinePanel);
        }

        const usersList = DOM.$('#onlineUsersList');
        usersList.innerHTML = users.map(user => `
            <div class="online-user-item ${user.status}" data-user-id="${user.id}">
                <div class="user-avatar">${user.avatar}</div>
                <div class="user-info">
                    <div class="user-name">${user.name}</div>
                    <div class="user-role">${this.getRoleText(user.role)}</div>
                </div>
                <div class="user-status ${user.status}"></div>
            </div>
        `).join('');
    }

    /**
     * 获取角色文本
     */
    getRoleText(role) {
        const roleTexts = {
            municipal_operator: '市级话务员',
            executor: '执行人员',
            manager: '管理者',
            callback_staff: '回访员'
        };
        return roleTexts[role] || role;
    }

    /**
     * 初始化协同工作功能
     */
    initCollaborativeFeatures() {
        // 添加协作工具栏
        this.addCollaborationToolbar();

        // 初始化工单锁定机制
        this.initTicketLocking();

        // 初始化协作提醒
        this.initCollaborationAlerts();
    }

    /**
     * 添加协作工具栏
     */
    addCollaborationToolbar() {
        const toolbar = DOM.$('.toolbar-right');
        if (!toolbar) return;

        const collaborationTools = document.createElement('div');
        collaborationTools.className = 'collaboration-tools';
        collaborationTools.innerHTML = `
            <button class="btn btn-icon" title="协作模式" id="collaborationMode">
                🤝
            </button>
            <button class="btn btn-icon" title="实时聊天" id="realtimeChat">
                💬
            </button>
            <button class="btn btn-icon" title="共享屏幕" id="screenShare">
                📺
            </button>
        `;

        toolbar.insertBefore(collaborationTools, toolbar.firstChild);

        // 绑定协作功能事件
        EventHandler.on(DOM.$('#collaborationMode'), 'click', () => {
            this.toggleCollaborationMode();
        });

        EventHandler.on(DOM.$('#realtimeChat'), 'click', () => {
            this.openRealtimeChat();
        });

        EventHandler.on(DOM.$('#screenShare'), 'click', () => {
            this.startScreenShare();
        });
    }

    /**
     * 切换协作模式
     */
    toggleCollaborationMode() {
        this.collaborationMode = !this.collaborationMode;

        if (this.collaborationMode) {
            document.body.classList.add('collaboration-mode');
            Message.success('协作模式已开启');
            this.startCollaborationSession();
        } else {
            document.body.classList.remove('collaboration-mode');
            Message.success('协作模式已关闭');
            this.endCollaborationSession();
        }
    }

    /**
     * 开始协作会话
     */
    startCollaborationSession() {
        // 显示协作面板
        this.showCollaborationPanel();

        // 启用实时光标同步
        this.enableCursorSync();

        // 启用实时选择同步
        this.enableSelectionSync();
    }

    /**
     * 显示协作面板
     */
    showCollaborationPanel() {
        const panel = document.createElement('div');
        panel.className = 'collaboration-panel';
        panel.innerHTML = `
            <div class="panel-header">
                <h4>协作会话</h4>
                <button class="panel-close" onclick="this.closest('.collaboration-panel').remove()">×</button>
            </div>
            <div class="panel-content">
                <div class="collaboration-participants">
                    <h5>参与者</h5>
                    <div id="collaborationParticipants"></div>
                </div>
                <div class="collaboration-actions">
                    <button class="btn btn-sm btn-primary" onclick="ticketListController.inviteCollaborator()">
                        邀请协作
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="ticketListController.shareView()">
                        共享视图
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);
    }

    /**
     * 邀请协作者
     */
    inviteCollaborator() {
        // 显示邀请对话框
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content invite-collaborator-modal">
                <div class="modal-header">
                    <h3>邀请协作</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>选择协作者：</label>
                        <select id="collaboratorSelect" multiple>
                            <option value="user1">张三 - 市级话务员</option>
                            <option value="user2">李四 - 执行人员</option>
                            <option value="user3">王五 - 管理者</option>
                            <option value="user4">赵六 - 回访员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>协作内容：</label>
                        <textarea id="collaborationMessage" placeholder="请描述需要协作的内容..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="ticketListController.sendCollaborationInvite()">发送邀请</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * 发送协作邀请
     */
    sendCollaborationInvite() {
        const selectedUsers = Array.from(DOM.$('#collaboratorSelect').selectedOptions).map(option => option.value);
        const message = DOM.$('#collaborationMessage').value;

        console.log('发送协作邀请:', { users: selectedUsers, message });

        // 模拟发送邀请
        Message.success(`已向 ${selectedUsers.length} 位用户发送协作邀请`);

        DOM.$('.modal-overlay')?.remove();
    }
}

// 导出控制器（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TicketListController;
}
