/* 布局样式 */

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.breadcrumb .separator {
    color: var(--text-disabled);
}

.breadcrumb .current {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-role {
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-radius: var(--border-radius-sm);
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: #ffffff;
    background-color: var(--primary-color);
    border-radius: 50%;
}

/* 主要内容区域 */
.main-content {
    padding: var(--spacing-lg);
    max-width: 1600px;
    margin: 0 auto;
}

/* 统计面板 */
.dashboard-panel {
    margin-bottom: var(--spacing-lg);
}

.stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background-color: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.stat-card:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
}

.stat-card.urgent {
    background-color: var(--stat-urgent-bg);
    border-left: 4px solid var(--error-color);
}

.stat-card.warning {
    background-color: var(--stat-warning-bg);
    border-left: 4px solid var(--warning-color);
}

.stat-card.pending {
    background-color: var(--stat-pending-bg);
    border-left: 4px solid var(--primary-color);
}

.stat-card.merge {
    background-color: var(--stat-merge-bg);
    border-left: 4px solid var(--success-color);
}

.stat-icon {
    font-size: 32px;
    opacity: 0.8;
}

.stat-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-number {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

/* 筛选区域 - 紧凑横向布局 */
.filter-section {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.filter-toolbar {
    padding: var(--spacing-md) var(--spacing-lg);
}

.quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    white-space: nowrap;
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-right: var(--spacing-xs);
    flex-shrink: 0;
}

.filter-buttons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.filter-btn {
    padding: 4px var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.filter-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.filter-btn.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.filter-select {
    min-width: 120px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
}

/* 搜索区域 - 整合到筛选区域 */
.search-area {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-left: auto;
    flex-shrink: 0;
}

.search-input-group {
    display: flex;
    width: 300px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    height: 32px;
}

.search-input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.search-input {
    flex: 1;
    padding: 6px var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: none;
    background-color: var(--bg-white);
    height: 100%;
}

.search-input::placeholder {
    color: var(--text-placeholder);
}

.search-btn {
    padding: 0 var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    height: 100%;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.advanced-search-btn {
    padding: 4px var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    background-color: transparent;
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.advanced-search-btn:hover {
    background-color: var(--primary-light);
}

/* 角色专用筛选 */
.role-specific-filters {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-gray);
    border-top: 1px solid var(--border-light);
    display: none;
}

.role-specific-filters.show {
    display: block;
}

.role-filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
}

.role-filter-group:last-child {
    margin-bottom: 0;
}

.role-filter-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    min-width: 80px;
    flex-shrink: 0;
}

.role-filter-group .filter-buttons {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.role-filter-group .filter-btn {
    padding: 4px var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
}

.role-filter-group .filter-btn:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.role-filter-group .filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    font-weight: var(--font-weight-medium);
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.view-controls {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.view-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    background-color: var(--bg-white);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.view-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.view-btn.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.view-btn + .view-btn {
    border-left: 1px solid var(--border-light);
}

/* 批量操作提示 */
.batch-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--batch-bg);
    border: 1px solid var(--batch-border);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.batch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.batch-text {
    font-size: var(--font-size-sm);
    color: var(--batch-text);
    font-weight: var(--font-weight-medium);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 表格区域 */
.table-section {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table-container {
    overflow-x: auto;
    position: relative;
    border-radius: var(--border-radius-md);
}

/* 顶部滚动条容器 */
.top-scrollbar-container {
    overflow-x: auto;
    overflow-y: hidden;
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-light);
    border: 1px solid var(--border-light);
    position: relative;
}

.top-scrollbar-container::before {
    content: "← 拖动滚动条查看更多列 →";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    pointer-events: none;
    z-index: 1;
}

.top-scrollbar-content {
    height: 20px;
    /* 宽度将通过JavaScript动态设置，与表格宽度一致 */
}

/* 优化滚动条样式 */
.table-container::-webkit-scrollbar {
    height: 8px;
}

.top-scrollbar-container::-webkit-scrollbar {
    height: 12px;
}

.table-container::-webkit-scrollbar-track,
.top-scrollbar-container::-webkit-scrollbar-track {
    background: var(--bg-light);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb,
.top-scrollbar-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover,
.top-scrollbar-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* 顶部滚动条特殊样式 */
.top-scrollbar-container::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* 加载和空状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    gap: var(--spacing-md);
}

.loading-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 分页区域 */
.pagination-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.page-size-select {
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-white);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .stat-cards {
        grid-template-columns: 1fr;
    }
    
    .quick-filters {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .filter-group label {
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
    }

    .filter-buttons {
        width: 100%;
        justify-content: flex-start;
    }

    .search-area {
        margin-left: 0;
        width: 100%;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .search-input-group {
        width: 100%;
    }
    
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .search-area {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input-group {
        max-width: none;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }
}

/* 高级搜索弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
}

.modal-close:hover {
    background-color: var(--bg-light);
    color: var(--text-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.search-form .form-row {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.search-form .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.search-form .form-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.search-form select,
.search-form input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.search-form select:focus,
.search-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--border-light);
}

/* 智能搜索样式 */
.intelligent-search-container {
    position: relative;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.suggestion-group {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.suggestion-group:last-child {
    border-bottom: none;
}

.suggestion-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

.suggestion-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.suggestion-item {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.suggestion-item:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.suggestion-item.intelligent {
    background-color: rgba(24, 144, 255, 0.1);
    border-left: 3px solid var(--info-color);
}

.suggestion-item.similar {
    background-color: rgba(82, 196, 26, 0.1);
    border-left: 3px solid var(--success-color);
}

.suggestion-item.exception {
    background-color: rgba(255, 77, 79, 0.1);
    border-left: 3px solid var(--error-color);
}

/* 智能通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    max-width: 400px;
}

.intelligent-notification {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
    position: relative;
    border-left: 4px solid var(--info-color);
    animation: slideInRight 0.3s ease-out;
}

.intelligent-notification.warning {
    border-left-color: var(--warning-color);
}

.intelligent-notification.error {
    border-left-color: var(--error-color);
}

.intelligent-notification.success {
    border-left-color: var(--success-color);
}

.notification-content {
    padding-right: var(--spacing-lg);
}

.notification-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.notification-close {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-secondary);
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
}

.notification-close:hover {
    background-color: var(--bg-light);
    color: var(--text-color);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 智能选择按钮样式 */
.intelligent-selection {
    display: flex;
    gap: var(--spacing-xs);
    margin-right: var(--spacing-md);
    padding-right: var(--spacing-md);
    border-right: 1px solid var(--border-light);
}

.intelligent-selection .btn {
    font-size: var(--font-size-xs);
    padding: 4px var(--spacing-xs);
    height: 28px;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-outline:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-sm {
    font-size: var(--font-size-xs);
    padding: 4px var(--spacing-sm);
}

/* 显示密度样式 */
.density-compact .ticket-table td,
.density-compact .ticket-table th {
    padding: 4px var(--spacing-xs);
    font-size: var(--font-size-xs);
    line-height: 1.2;
}

.density-normal .ticket-table td,
.density-normal .ticket-table th {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.density-comfortable .ticket-table td,
.density-comfortable .ticket-table th {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.6;
}

/* 主题样式 */
.theme-dark {
    --bg-white: #1f1f1f;
    --bg-light: #2d2d2d;
    --bg-gray: #3d3d3d;
    --text-color: #ffffff;
    --text-secondary: #cccccc;
    --text-disabled: #888888;
    --border-color: #444444;
    --border-light: #333333;
}

.theme-light {
    --bg-white: #ffffff;
    --bg-light: #f5f5f5;
    --bg-gray: #fafafa;
    --text-color: #333333;
    --text-secondary: #666666;
    --text-disabled: #999999;
    --border-color: #d9d9d9;
    --border-light: #f0f0f0;
}

/* 自动保存指示器 */
.auto-save-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--success-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    z-index: 1500;
    display: none;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* 连接状态指示器 */
.connection-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    z-index: 1500;
    animation: slideInUp 0.3s ease-out;
}

.connection-status.connected {
    background-color: var(--success-color);
    color: white;
}

.connection-status.disconnected {
    background-color: var(--error-color);
    color: white;
}

.connection-status.connecting {
    background-color: var(--warning-color);
    color: white;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 在线用户面板 */
.online-users-panel {
    position: fixed;
    top: 100px;
    right: 20px;
    width: 250px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.online-users-panel.collapsed .panel-content {
    display: none;
}

.online-users-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
}

.online-users-panel .panel-toggle {
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.online-users-panel .panel-content {
    max-height: 300px;
    overflow-y: auto;
}

.online-user-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    transition: background-color var(--transition-normal);
}

.online-user-item:hover {
    background-color: var(--bg-light);
}

.online-user-item:last-child {
    border-bottom: none;
}

.online-user-item .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: var(--spacing-sm);
}

.online-user-item .user-info {
    flex: 1;
}

.online-user-item .user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.online-user-item .user-role {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.online-user-item .user-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: var(--spacing-xs);
}

.online-user-item .user-status.online {
    background-color: var(--success-color);
}

.online-user-item .user-status.busy {
    background-color: var(--warning-color);
}

.online-user-item .user-status.away {
    background-color: var(--text-disabled);
}

/* 协作面板 */
.collaboration-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1500;
}

.collaboration-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.collaboration-panel .panel-header h4 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.collaboration-panel .panel-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
}

.collaboration-panel .panel-content {
    padding: var(--spacing-md);
}

.collaboration-participants h5 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.collaboration-actions {
    margin-top: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
}

/* 表格更新动画 */
.ticket-table tbody tr.updating {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.ticket-table tbody tr.updated {
    background-color: rgba(82, 196, 26, 0.1);
    animation: highlightUpdate 2s ease-out;
}

@keyframes highlightUpdate {
    0% {
        background-color: rgba(82, 196, 26, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

/* 协作模式样式 */
.collaboration-mode .ticket-table {
    border: 2px solid var(--primary-color);
}

.collaboration-mode .collaboration-tools .btn {
    background-color: var(--primary-color);
    color: white;
}

/* 分组结果模态框样式 */
.grouping-results-modal {
    max-width: 800px;
    width: 90%;
}

.groups-container {
    max-height: 500px;
    overflow-y: auto;
}

.group-item {
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-light);
}

.group-header h4 {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.group-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.group-tickets {
    padding: var(--spacing-sm);
}

.group-ticket-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
    font-size: var(--font-size-sm);
}

.group-ticket-item:last-child {
    border-bottom: none;
}

.group-ticket-item .ticket-number {
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
}

.group-ticket-item .ticket-title {
    flex: 1;
    margin: 0 var(--spacing-sm);
    color: var(--text-color);
}

.group-ticket-item .ticket-status {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 批量处理建议模态框 */
.batch-recommendations-modal {
    max-width: 600px;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-light);
}

.recommendation-item.warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
}

.recommendation-item.info {
    background-color: rgba(24, 144, 255, 0.1);
    border-color: var(--info-color);
}

.recommendation-item.success {
    background-color: rgba(82, 196, 26, 0.1);
    border-color: var(--success-color);
}

.recommendation-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.recommendation-content {
    flex: 1;
}

.recommendation-content h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.recommendation-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.recommendation-action {
    flex-shrink: 0;
}
