/**
 * 表格组件
 */

class TicketTable {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            selectable: true,
            sortable: true,
            columns: CONFIG.columns.default,
            ...options
        };
        
        this.data = [];
        this.selectedIds = new Set();
        this.sortField = CONFIG.table.defaultSortField;
        this.sortOrder = CONFIG.table.defaultSortOrder;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUserSettings();
        this.initScrollbarSync();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 全选/反选
        EventHandler.on(this.container, 'change', (e) => {
            if (e.target.id === 'selectAll') {
                this.toggleSelectAll(e.target.checked);
            } else if (e.target.classList.contains('table-checkbox')) {
                this.toggleRowSelection(e.target);
            }
        });
        
        // 排序
        EventHandler.delegate(this.container, '.sortable', 'click', (e) => {
            const field = e.currentTarget.dataset.sort;
            this.sort(field);
        });
        
        // 行点击
        EventHandler.delegate(this.container, 'tbody tr', 'click', (e) => {
            if (!e.target.classList.contains('table-checkbox') && 
                !e.target.closest('.action-btn') && 
                !e.target.closest('.more-actions')) {
                this.selectRow(e.currentTarget);
            }
        });
        
        // 操作按钮
        EventHandler.delegate(this.container, '.action-btn', 'click', (e) => {
            e.stopPropagation();
            const action = e.currentTarget.dataset.action;
            const row = e.currentTarget.closest('tr');
            const ticketId = parseInt(row.dataset.id);
            this.handleAction(action, ticketId);
        });
        
        // 更多操作
        EventHandler.delegate(this.container, '.more-actions .action-btn', 'click', (e) => {
            e.stopPropagation();
            const dropdown = e.currentTarget.closest('.more-actions');
            DOM.toggleClass(dropdown, 'active');
        });
        
        // 点击外部关闭下拉菜单
        EventHandler.on(document, 'click', (e) => {
            if (!e.target.closest('.more-actions')) {
                DOM.$$('.more-actions.active').forEach(dropdown => {
                    DOM.removeClass(dropdown, 'active');
                });
            }
        });
    }
    
    /**
     * 渲染表格
     */
    render(data = []) {
        this.data = data;
        const tbody = DOM.$('#ticketTableBody');
        
        if (!tbody) return;
        
        if (data.length === 0) {
            this.renderEmpty();
            return;
        }
        
        tbody.innerHTML = data.map(ticket => this.renderRow(ticket)).join('');
        this.updateSelectAllState();
        this.updateRowStyles();
    }
    
    /**
     * 渲染单行
     */
    renderRow(ticket) {
        const isSelected = this.selectedIds.has(ticket.id);
        const urgencyClass = ticket.urgency === 'critical' ? 'urgent' :
                           ticket.timeLimit < Date.now() ? 'timeout' : '';

        // 将中文关联类型转换为CSS类名
        const relationClass = this.getRelationCssClass(ticket.relationType);

        return `
            <tr data-id="${ticket.id}" class="${isSelected ? 'selected' : ''} ${urgencyClass} ${relationClass}">
                <td class="checkbox-col">
                    <input type="checkbox" class="table-checkbox" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <span class="ticket-number" data-ticket="${ticket.ticketNumber}">
                        ${ticket.ticketNumber}
                    </span>
                </td>
                <td>
                    <div class="status-cell">
                        <span class="status-dot ${ticket.status}"></span>
                        <span class="status-text">${this.getStatusText(ticket.status)}</span>
                    </div>
                </td>
                <td>
                    <div class="mode-cell">
                        <span class="mode-indicator ${ticket.mode}">
                            ${CONFIG.icons.mode[ticket.mode]} ${this.getModeText(ticket.mode)}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="urgency-cell">
                        <span class="urgency-indicator ${ticket.urgency}">
                            ${CONFIG.icons.urgency[ticket.urgency]} ${this.getUrgencyText(ticket.urgency)}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="supervision-cell">
                        <span class="supervision-level ${ticket.supervisionLevel !== '无' ? 'has-supervision' : ''}">
                            ${ticket.supervisionLevel !== '无' ? '🚨' : ''} ${ticket.supervisionLevel}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="stage-cell">
                        <span class="current-stage">${ticket.currentStage || '未知'}</span>
                    </div>
                </td>
                <td>
                    <div class="collaboration-cell">
                        <span class="collaboration-mode ${ticket.collaborationMode !== '单独处理' ? 'has-collaboration' : ''}">
                            ${ticket.collaborationMode !== '单独处理' ? '🤝' : ''} ${ticket.collaborationMode}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="relation-cell">
                        <span class="relation-type ${ticket.relationType ? 'has-relation' : ''}">
                            ${this.getRelationIcon(ticket.relationType)} ${this.getRelationTypeText(ticket.relationType)}
                        </span>
                    </div>
                </td>
                <td class="title-cell">
                    <span class="title-text" title="${ticket.title}">
                        ${ticket.title}
                    </span>
                </td>
                <td>
                    <div class="citizen-cell">
                        <div class="citizen-name">${ticket.citizen?.name || '未知'}</div>
                        <div class="citizen-phone">${ticket.citizen?.phone || ''}</div>
                        ${this.renderCitizenTags(ticket.citizen?.tags || [])}
                    </div>
                </td>
                <td>
                    <div class="vip-cell">
                        <span class="vip-level ${ticket.vipLevel !== '无' ? 'is-vip' : ''}">
                            ${ticket.vipLevel !== '无' ? '⭐' : ''} ${ticket.vipLevel}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="customer-type-cell">
                        <span class="customer-type">${ticket.customerType}</span>
                    </div>
                </td>
                <td>
                    <div class="history-cell">
                        <span class="history-count ${ticket.historyCount > 3 ? 'frequent' : ''}">${ticket.historyCount}</span>
                    </div>
                </td>
                <td>
                    <div class="department-cell">
                        <div class="department-name">${ticket.department || '未分配'}</div>
                        ${ticket.currentHandler ? `<div class="department-person">${ticket.currentHandler}</div>` : ''}
                    </div>
                </td>
                <td>
                    <div class="main-dept-cell">
                        <span class="main-department">${ticket.mainDepartment || (ticket.collaborationMode === '主协办' ? ticket.department : '-')}</span>
                    </div>
                </td>
                <td>
                    <div class="assist-dept-cell">
                        <span class="assist-departments" title="${ticket.assistDepartments || ''}">${ticket.assistDepartments || '-'}</span>
                    </div>
                </td>
                <td class="area-cell">
                    <span class="area-text" title="${ticket.area || ''}">
                        ${this.formatArea(ticket.area || '')}
                    </span>
                </td>
                <td class="time-cell">
                    ${UTILS.formatTime(ticket.createTime, 'MM-DD HH:mm')}
                </td>
                <td>
                    <div class="time-limit-cell">
                        ${this.renderTimeLimit(ticket)}
                    </div>
                </td>
                <td>
                    <div class="stage-time-limit-cell">
                        ${this.renderStageTimeLimit(ticket)}
                    </div>
                </td>
                <td>
                    <div class="overtime-cell">
                        <span class="overtime-status ${ticket.overtimeStatus}">
                            ${this.getOvertimeIcon(ticket.overtimeStatus)} ${ticket.overtimeStatus}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="satisfaction-cell">
                        <span class="satisfaction-score ${this.getSatisfactionClass(ticket.satisfaction)}">
                            ${this.renderSatisfaction(ticket.satisfaction)}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="callback-cell">
                        <span class="callback-result ${ticket.callbackResult}">
                            ${this.getCallbackIcon(ticket.callbackResult)} ${ticket.callbackResult}
                        </span>
                    </div>
                </td>
                <td>
                    <div class="restart-cell">
                        <span class="restart-count ${ticket.restartCount > 0 ? 'has-restart' : ''}">${ticket.restartCount}</span>
                    </div>
                </td>
                <td class="actions-col">
                    <div class="actions-cell">
                        ${this.renderActions(ticket)}
                    </div>
                </td>
            </tr>
        `;
    }
    
    /**
     * 渲染市民标签
     */
    renderCitizenTags(tags) {
        if (!tags || tags.length === 0) return '';

        return `
            <div class="citizen-tags">
                ${tags.map(tag => `<span class="citizen-tag ${tag}">${this.getCitizenTagText(tag)}</span>`).join('')}
            </div>
        `;
    }

    /**
     * 获取关联类型图标
     */
    getRelationIcon(relationType) {
        const icons = {
            '主工单': '📊',
            '被合并': '🔗',
            '子工单': '📄',
            '拆分工单': '📋'
        };
        return icons[relationType] || '';
    }

    /**
     * 获取关联类型中文文本
     */
    getRelationTypeText(relationType) {
        // 如果已经是中文，直接返回
        if (!relationType) return '独立';

        // 英文到中文的映射（兼容可能的英文数据）
        const relationTexts = {
            'merged': '已合并',
            'split': '已拆分',
            'parent': '主工单',
            'child': '子工单',
            // 中文数据直接映射
            '主工单': '主工单',
            '被合并': '已合并',
            '子工单': '子工单',
            '拆分工单': '已拆分'
        };

        return relationTexts[relationType] || relationType || '独立';
    }

    /**
     * 获取关联类型CSS类名
     */
    getRelationCssClass(relationType) {
        const classMap = {
            '主工单': 'parent',
            '被合并': 'merged',
            '子工单': 'child',
            '拆分工单': 'split'
        };
        return classMap[relationType] || '';
    }

    /**
     * 渲染环节时限
     */
    renderStageTimeLimit(ticket) {
        if (!ticket.currentStageTimeLimit) return '-';

        const remaining = ticket.currentStageTimeLimit - Date.now();
        const hours = Math.floor(remaining / (1000 * 60 * 60));

        if (remaining <= 0) {
            return '<span class="time-expired">已超时</span>';
        } else if (hours < 4) {
            return `<span class="time-urgent">${hours}小时</span>`;
        } else {
            return `<span class="time-normal">${hours}小时</span>`;
        }
    }

    /**
     * 获取超时状态图标
     */
    getOvertimeIcon(status) {
        const icons = {
            '正常': '🟢',
            '即将超时': '🟡',
            '已超时': '🔴'
        };
        return icons[status] || '';
    }

    /**
     * 渲染满意度
     */
    renderSatisfaction(satisfaction) {
        if (!satisfaction) return '-';

        const stars = '★'.repeat(satisfaction) + '☆'.repeat(5 - satisfaction);
        return `${stars} (${satisfaction})`;
    }

    /**
     * 获取满意度样式类
     */
    getSatisfactionClass(satisfaction) {
        if (!satisfaction) return '';
        if (satisfaction >= 4) return 'high';
        if (satisfaction >= 3) return 'medium';
        return 'low';
    }

    /**
     * 获取回访结果图标
     */
    getCallbackIcon(result) {
        const icons = {
            '满意': '😊',
            '基本满意': '😐',
            '不满意': '😞',
            '未回访': '❓'
        };
        return icons[result] || '';
    }

    /**
     * 初始化滚动条同步
     */
    initScrollbarSync() {
        const topScrollbar = DOM.$('#topScrollbar');
        const topScrollbarContent = DOM.$('#topScrollbarContent');
        const tableContainer = DOM.$('#tableContainer');
        const table = DOM.$('#ticketTable');

        if (!topScrollbar || !topScrollbarContent || !tableContainer || !table) {
            return;
        }

        // 设置顶部滚动条内容宽度与表格宽度一致
        const updateScrollbarWidth = () => {
            const tableWidth = table.scrollWidth;
            topScrollbarContent.style.width = tableWidth + 'px';
        };

        // 初始设置宽度
        updateScrollbarWidth();

        // 监听窗口大小变化
        window.addEventListener('resize', updateScrollbarWidth);

        // 同步滚动位置
        let isTopScrolling = false;
        let isTableScrolling = false;

        // 顶部滚动条滚动时，同步表格容器
        topScrollbar.addEventListener('scroll', () => {
            if (isTableScrolling) return;
            isTopScrolling = true;
            tableContainer.scrollLeft = topScrollbar.scrollLeft;
            setTimeout(() => { isTopScrolling = false; }, 10);
        });

        // 表格容器滚动时，同步顶部滚动条
        tableContainer.addEventListener('scroll', () => {
            if (isTopScrolling) return;
            isTableScrolling = true;
            topScrollbar.scrollLeft = tableContainer.scrollLeft;
            setTimeout(() => { isTableScrolling = false; }, 10);
        });

        // 数据更新时重新设置宽度
        const originalRender = this.render.bind(this);
        this.render = function(data) {
            originalRender(data);
            setTimeout(updateScrollbarWidth, 0);
        };
    }
    
    /**
     * 渲染协办信息
     */
    renderCollaborativeInfo(collaborative) {
        if (!collaborative) return '';
        
        return `
            <div class="collaborative-info">
                <span class="collaborative-tag main">主办</span>
                ${collaborative.assistDepartments.map(dept => 
                    `<span class="collaborative-tag assist">协办</span>`
                ).join('')}
            </div>
        `;
    }
    
    /**
     * 渲染时限信息
     */
    renderTimeLimit(ticket) {
        const timeDiff = UTILS.getTimeDiff(ticket.timeLimit);
        
        return `
            <span class="time-limit-text ${timeDiff.type}">
                ${timeDiff.text}
            </span>
        `;
    }
    
    /**
     * 渲染操作按钮
     */
    renderActions(ticket) {
        const actions = this.getAvailableActions(ticket);
        
        const primaryActions = actions.slice(0, 3);
        const moreActions = actions.slice(3);
        
        let html = primaryActions.map(action => 
            `<button class="action-btn ${action.type}" data-action="${action.type}" title="${action.label}">
                ${action.icon}
            </button>`
        ).join('');
        
        if (moreActions.length > 0) {
            html += `
                <div class="more-actions">
                    <button class="action-btn more" title="更多操作">
                        ${CONFIG.icons.actions.more}
                    </button>
                    <div class="more-actions-menu">
                        ${moreActions.map(action => 
                            `<button class="more-actions-item" data-action="${action.type}">
                                <span>${action.icon}</span>
                                <span>${action.label}</span>
                            </button>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        return html;
    }
    
    /**
     * 获取可用操作
     */
    getAvailableActions(ticket) {
        const actions = [];
        const currentUser = ticketListController.currentUser;
        if (!currentUser) return actions;

        const status = ticket.status;
        const role = currentUser.role;

        // 基础查看操作（所有角色都有）
        actions.push({
            type: 'view',
            label: '查看详情',
            icon: '👁️'
        });

        // 根据状态和角色添加特定操作
        switch (status) {
            case 'draft':
                if (role === 'municipal_operator') {
                    actions.push({
                        type: 'edit',
                        label: '编辑草稿',
                        icon: '✏️'
                    });
                    actions.push({
                        type: 'submit',
                        label: '提交工单',
                        icon: '📤'
                    });
                    actions.push({
                        type: 'delete',
                        label: '删除草稿',
                        icon: '🗑️'
                    });
                }
                break;

            case 'pending':
                if (role === 'municipal_operator') {
                    actions.push({
                        type: 'dispatch',
                        label: '派单',
                        icon: '📋'
                    });
                    if (ticket.instantHandleOpportunity === '可即时办结') {
                        actions.push({
                            type: 'instant',
                            label: '即时办结',
                            icon: '⚡'
                        });
                    }
                }
                if (role === 'executor') {
                    actions.push({
                        type: 'accept',
                        label: '接收工单',
                        icon: '✅'
                    });
                    actions.push({
                        type: 'reject',
                        label: '退回工单',
                        icon: '❌'
                    });
                }
                break;

            case 'processing':
                if (role === 'executor') {
                    actions.push({
                        type: 'update',
                        label: '更新进度',
                        icon: '📝'
                    });
                    actions.push({
                        type: 'complete',
                        label: '办结工单',
                        icon: '✅'
                    });
                    actions.push({
                        type: 'suspend',
                        label: '挂起工单',
                        icon: '⏸️'
                    });
                }
                if (role === 'manager') {
                    if (ticket.supervisionLevel === '无') {
                        actions.push({
                            type: 'supervise',
                            label: '督办',
                            icon: '🚨'
                        });
                    }
                    actions.push({
                        type: 'coordinate',
                        label: '协调',
                        icon: '🤝'
                    });
                }
                break;

            case 'review':
                if (role === 'manager') {
                    actions.push({
                        type: 'approve',
                        label: '审核通过',
                        icon: '✅'
                    });
                    actions.push({
                        type: 'return',
                        label: '退回修改',
                        icon: '↩️'
                    });
                }
                break;

            case 'callback':
                if (role === 'callback_staff') {
                    actions.push({
                        type: 'call',
                        label: '开始回访',
                        icon: '📞'
                    });
                    actions.push({
                        type: 'record',
                        label: '记录结果',
                        icon: '📝'
                    });
                }
                break;

            case 'closed':
                if (role === 'manager') {
                    actions.push({
                        type: 'reopen',
                        label: '重新开启',
                        icon: '🔄'
                    });
                }
                if (role === 'callback_staff' && ticket.satisfaction < 3) {
                    actions.push({
                        type: 'restart',
                        label: '重启工单',
                        icon: '🔄'
                    });
                }
                break;
        }

        // 特殊操作（根据工单特性）
        if (ticket.canMerge && role === 'municipal_operator') {
            actions.push({
                type: 'merge',
                label: '合并工单',
                icon: '🔗'
            });
        }

        if (ticket.relationType === 'parent' && role === 'municipal_operator') {
            actions.push({
                type: 'split',
                label: '拆分工单',
                icon: '📋'
            });
        }

        // VIP工单特殊处理
        if (ticket.vipLevel !== '无') {
            actions.push({
                type: 'vip',
                label: 'VIP专线',
                icon: '⭐'
            });
        }

        return actions;
    }
    
    /**
     * 渲染空状态
     */
    renderEmpty() {
        const tbody = DOM.$('#ticketTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="12" class="text-center p-xl">
                        <div class="empty-state">
                            <div class="empty-icon">📋</div>
                            <div class="empty-text">暂无工单数据</div>
                            <div class="empty-desc">请调整筛选条件或创建新工单</div>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
    
    /**
     * 排序
     */
    sort(field) {
        if (this.sortField === field) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortOrder = 'asc';
        }
        
        this.updateSortUI();
        EventHandler.trigger(this.container, 'sort-change', {
            field: this.sortField,
            order: this.sortOrder
        });
    }
    
    /**
     * 更新排序UI
     */
    updateSortUI() {
        // 清除所有排序状态
        DOM.$$('.sortable').forEach(th => {
            DOM.removeClass(th, 'sorted-asc');
            DOM.removeClass(th, 'sorted-desc');
        });
        
        // 设置当前排序状态
        const currentTh = DOM.$(`[data-sort="${this.sortField}"]`);
        if (currentTh) {
            DOM.addClass(currentTh, `sorted-${this.sortOrder}`);
        }
    }
    
    /**
     * 选择行
     */
    selectRow(row) {
        const id = parseInt(row.dataset.id);
        const checkbox = row.querySelector('.table-checkbox');
        
        if (this.selectedIds.has(id)) {
            this.selectedIds.delete(id);
            checkbox.checked = false;
            DOM.removeClass(row, 'selected');
        } else {
            this.selectedIds.add(id);
            checkbox.checked = true;
            DOM.addClass(row, 'selected');
        }
        
        this.updateSelectAllState();
        this.notifySelectionChange();
    }
    
    /**
     * 切换行选择
     */
    toggleRowSelection(checkbox) {
        const row = checkbox.closest('tr');
        const id = parseInt(row.dataset.id);
        
        if (checkbox.checked) {
            this.selectedIds.add(id);
            DOM.addClass(row, 'selected');
        } else {
            this.selectedIds.delete(id);
            DOM.removeClass(row, 'selected');
        }
        
        this.updateSelectAllState();
        this.notifySelectionChange();
    }
    
    /**
     * 全选/反选
     */
    toggleSelectAll(checked) {
        const checkboxes = DOM.$$('.table-checkbox:not(#selectAll)');
        const rows = DOM.$$('tbody tr');
        
        if (checked) {
            this.data.forEach(ticket => this.selectedIds.add(ticket.id));
            checkboxes.forEach(cb => cb.checked = true);
            rows.forEach(row => DOM.addClass(row, 'selected'));
        } else {
            this.selectedIds.clear();
            checkboxes.forEach(cb => cb.checked = false);
            rows.forEach(row => DOM.removeClass(row, 'selected'));
        }
        
        this.notifySelectionChange();
    }
    
    /**
     * 更新全选状态
     */
    updateSelectAllState() {
        const selectAllCheckbox = DOM.$('#selectAll');
        if (!selectAllCheckbox) return;
        
        const totalCount = this.data.length;
        const selectedCount = this.selectedIds.size;
        
        if (selectedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCount === totalCount) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
    
    /**
     * 通知选择变化
     */
    notifySelectionChange() {
        EventHandler.trigger(this.container, 'selection-change', {
            selectedIds: Array.from(this.selectedIds),
            selectedCount: this.selectedIds.size
        });
    }
    
    /**
     * 处理操作
     */
    handleAction(action, ticketId) {
        EventHandler.trigger(this.container, 'action', {
            action: action,
            ticketId: ticketId
        });
    }
    
    /**
     * 更新行样式
     */
    updateRowStyles() {
        const rows = DOM.$$('tbody tr');
        rows.forEach(row => {
            const id = parseInt(row.dataset.id);
            const ticket = this.data.find(t => t.id === id);
            
            if (ticket) {
                // 紧急程度样式
                if (ticket.urgency === 'critical') {
                    DOM.addClass(row, 'urgent');
                }
                
                // 超时样式
                if (ticket.timeLimit < Date.now()) {
                    DOM.addClass(row, 'timeout');
                }
                
                // 关联关系样式
                if (ticket.relationType) {
                    DOM.addClass(row, ticket.relationType);
                }
            }
        });
    }
    
    /**
     * 获取选中的工单
     */
    getSelectedTickets() {
        return this.data.filter(ticket => this.selectedIds.has(ticket.id));
    }
    
    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedIds.clear();
        DOM.$$('.table-checkbox').forEach(cb => cb.checked = false);
        DOM.$$('tbody tr').forEach(row => DOM.removeClass(row, 'selected'));
        this.updateSelectAllState();
        this.notifySelectionChange();
    }
    
    /**
     * 工具方法
     */
    getStatusText(status) {
        const texts = {
            draft: '草稿',
            pending: '待接收',
            processing: '处理中',
            review: '待审核',
            callback: '待回访',
            closed: '已关闭',
            cancelled: '已废除',
            suspended: '挂起'
        };
        return texts[status] || status;
    }
    
    getUrgencyText(urgency) {
        const texts = {
            normal: '一般',
            urgent: '紧急',
            critical: '特急'
        };
        return texts[urgency] || urgency;
    }
    
    getModeText(mode) {
        const texts = {
            instant: '即时办结',
            normal: '普通流转',
            collaborative: '主协办',
            supervise: '督办'
        };
        return texts[mode] || mode;
    }
    
    getCitizenTagText(tag) {
        const texts = {
            vip: 'VIP',
            repeat: '重复',
            media: '媒体',
            leader: '批示'
        };
        return texts[tag] || tag;
    }
    
    formatArea(area) {
        const parts = area.split('/');
        return parts.slice(-2).join('/'); // 只显示最后两级
    }
    
    /**
     * 加载用户设置
     */
    loadUserSettings() {
        const settings = UTILS.storage.get(CONFIG.storageKeys.columnSettings);
        if (settings) {
            this.options.columns = settings.columns || this.options.columns;
        }
    }
    
    /**
     * 保存用户设置
     */
    saveUserSettings() {
        const settings = {
            columns: this.options.columns,
            sortField: this.sortField,
            sortOrder: this.sortOrder
        };
        UTILS.storage.set(CONFIG.storageKeys.columnSettings, settings);
    }
}

// 导出组件（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TicketTable;
}
